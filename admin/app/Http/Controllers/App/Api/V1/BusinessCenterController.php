<?php

namespace App\Http\Controllers\App\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BusinessCenterController extends Controller
{
    /**
     * 获取业务员工作台数据
     */
    public function getSalesmanWorkspace(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            Log::info("APP业务员工作台API - 用户ID: {$user->id}");
            
            // 验证用户是否为销售人员
            $salesman = DB::table('salesmen')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$salesman) {
                // 如果没有业务员记录，但用户有业务员角色，自动创建
                $appUser = DB::table('app_users')->where('id', $user->id)->first();
                if ($appUser && isset($appUser->is_salesman) && $appUser->is_salesman == 1) {
                    // 自动创建业务员记录
                    $salesmanId = DB::table('salesmen')->insertGetId([
                        'user_id' => $user->id,
                        'employee_id' => 'S' . str_pad($user->id, 6, '0', STR_PAD_LEFT),
                        'title' => '业务员',
                        'status' => 'active',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    
                    $salesman = DB::table('salesmen')->where('id', $salesmanId)->first();
                    Log::info("自动创建业务员记录 - 用户ID: {$user->id}, 业务员ID: {$salesmanId}");
                } else {
                    return response()->json([
                        'code' => 1,
                        'message' => '您不是销售人员，无权访问',
                        'data' => null
                    ]);
                }
            }
            
            // 获取当前日期
            $today = Carbon::today()->format('Y-m-d');
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $currentMonth = Carbon::today()->format('Y-m');
            $lastMonth = Carbon::today()->subMonth()->format('Y-m');
            
            // 获取用户信息
            $userInfo = DB::table('app_users')->where('id', $user->id)->first();
            
            // 使用tapp_devices表统计业务员销售的设备（is_self_use=0表示销售给客户的设备）
            // 获取今日销售数据
            $todaySales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->whereDate('created_at', $today)
                ->where('is_self_use', 0) // 只统计销售给客户的设备
                ->count();
                
            // 获取昨日销售数据
            $yesterdaySales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->whereDate('created_at', $yesterday)
                ->where('is_self_use', 0)
                ->count();
                
            // 获取本月销售数据
            $currentMonthSales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->where('is_self_use', 0)
                ->count();
                
            // 获取上月销售数据
            $lastMonthSales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$lastMonth])
                ->where('is_self_use', 0)
                ->count();
                
            // 获取总销售数据
            $totalSales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->where('is_self_use', 0)
                ->count();
                
            // 获取自用设备数量
            $selfUseDevices = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->where('is_self_use', 1)
                ->count();
                
            // 计算增长率
            $todayGrowthRate = $yesterdaySales > 0 ? round((($todaySales - $yesterdaySales) / $yesterdaySales) * 100, 2) : 0;
            $monthGrowthRate = $lastMonthSales > 0 ? round((($currentMonthSales - $lastMonthSales) / $lastMonthSales) * 100, 2) : 0;
            
            // 获取最近的客户（推荐的用户）
            $recentCustomers = DB::table('app_users')
                ->where('referrer_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->select(['id', 'name', 'phone', 'wechat_avatar', 'is_vip', 'created_at'])
                ->get();
                
            // 获取业绩排名
            $ranking = $this->getSalesmanRanking($user->id);
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'user_info' => [
                        'id' => $userInfo->id,
                        'name' => $userInfo->name ?: '业务员',
                        'avatar' => $userInfo->wechat_avatar ?: $userInfo->avatar ?: '',
                        'phone' => $userInfo->phone ?: '',
                        'employee_id' => $salesman->employee_id,
                        'title' => $salesman->title ?: '业务员',
                        'department' => $salesman->department ?: '销售部'
                    ],
                    'sales_stats' => [
                        'today_sales' => $todaySales,
                        'yesterday_sales' => $yesterdaySales,
                        'month_sales' => $currentMonthSales,
                        'last_month_sales' => $lastMonthSales,
                        'total_sales' => $totalSales,
                        'self_use_devices' => $selfUseDevices,
                        'today_growth_rate' => $todayGrowthRate,
                        'month_growth_rate' => $monthGrowthRate
                    ],
                    'recent_customers' => $recentCustomers,
                    'ranking' => $ranking
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error("业务员工作台API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取业务员统计数据
     */
    public function getSalesmanStats(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }
            
            $type = $request->input('type', 'overview'); // overview, detail, trend
            
            // 获取基础统计数据
            $stats = $this->calculateSalesmanStats($user->id, $type);
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            Log::error("业务员统计API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取业务员佣金记录
     */
    public function getSalesmanCommissions(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }
            
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $type = $request->input('type', 'all'); // all, pending, paid
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            
            // 构建查询
            $query = DB::table('salesman_commissions as sc')
                ->leftJoin('app_users as u', 'sc.customer_id', '=', 'u.id')
                ->where('sc.salesman_id', $user->id)
                ->select([
                    'sc.id',
                    'sc.amount',
                    'sc.type',
                    'sc.status',
                    'sc.description',
                    'sc.created_at',
                    'sc.paid_at',
                    'u.name as customer_name',
                    'u.phone as customer_phone'
                ]);
            
            if ($type !== 'all') {
                $query->where('sc.status', $type);
            }
            
            if ($startDate) {
                $query->whereDate('sc.created_at', '>=', $startDate);
            }
            
            if ($endDate) {
                $query->whereDate('sc.created_at', '<=', $endDate);
            }
            
            $total = $query->count();
            $commissions = $query->orderBy('sc.created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get();
            
            // 计算佣金统计
            $commissionStats = $this->calculateCommissionStats($user->id);
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $commissions,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'stats' => $commissionStats
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error("业务员佣金API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取业务员排名
     */
    private function getSalesmanRanking($userId)
    {
        // 获取当前用户在本月的排名
        $currentMonth = Carbon::now()->format('Y-m');
        
        $ranking = DB::select("
            SELECT 
                rank_table.rank,
                rank_table.total_sales,
                rank_table.total_users
            FROM (
                SELECT 
                    u.id,
                    COUNT(d.id) as total_sales,
                    COUNT(DISTINCT d.app_user_id) as total_users,
                    ROW_NUMBER() OVER (ORDER BY COUNT(d.id) DESC) as rank
                FROM app_users u
                LEFT JOIN tapp_devices d ON d.app_user_id = u.id 
                    AND d.is_self_use = 0 
                    AND DATE_FORMAT(d.created_at, '%Y-%m') = ?
                WHERE u.is_salesman = 1
                GROUP BY u.id
            ) rank_table
            WHERE rank_table.id = ?
        ", [$currentMonth, $userId]);
        
        return $ranking ? $ranking[0] : (object)['rank' => 0, 'total_sales' => 0, 'total_users' => 0];
    }
    
    /**
     * 计算业务员统计数据
     */
    private function calculateSalesmanStats($userId, $type = 'overview')
    {
        $currentMonth = Carbon::now()->format('Y-m');
        $currentYear = Carbon::now()->year;
        
        $stats = [
            'total_customers' => 0,
            'month_customers' => 0,
            'total_sales' => 0,
            'month_sales' => 0,
            'total_commission' => 0,
            'month_commission' => 0
        ];
        
        // 总客户数
        $stats['total_customers'] = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->count();
            
        // 本月新增客户
        $stats['month_customers'] = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
            ->count();
            
        // 总销售数量
        $stats['total_sales'] = DB::table('tapp_devices')
            ->where('app_user_id', $userId)
            ->where('is_self_use', 0)
            ->count();
            
        // 本月销售数量
        $stats['month_sales'] = DB::table('tapp_devices')
            ->where('app_user_id', $userId)
            ->where('is_self_use', 0)
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
            ->count();
            
        // 佣金统计
        $commissionStats = $this->calculateCommissionStats($userId);
        $stats['total_commission'] = $commissionStats['total_amount'];
        $stats['month_commission'] = $commissionStats['month_amount'];
        
        return $stats;
    }
    
    /**
     * 获取业务员客户列表
     */
    public function getSalesmanCustomers(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $status = $request->input('status', 'all'); // all, potential, contacted, converted
            $keyword = $request->input('keyword');

            // 构建查询
            $query = DB::table('app_users')
                ->where('referrer_id', $user->id)
                ->select([
                    'id',
                    'name',
                    'phone',
                    'wechat_avatar as avatar',
                    'is_vip',
                    'vip_at',
                    'created_at'
                ]);

            // 状态筛选
            if ($status === 'potential') {
                $query->where('is_vip', 0);
            } elseif ($status === 'converted') {
                $query->where('is_vip', 1);
            } elseif ($status === 'contacted') {
                $query->where('is_vip', 0)
                      ->where('created_at', '<', Carbon::now()->subDay());
            }

            // 关键词搜索
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%");
                });
            }

            $total = $query->count();
            $customers = $query->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get();

            // 处理客户数据
            $customerList = $customers->map(function($customer) {
                return [
                    'id' => $customer->id,
                    'name' => $customer->name ?: '未设置',
                    'phone' => $customer->phone ?: '',
                    'avatar' => $customer->avatar ?: '',
                    'status' => $this->getCustomerStatus($customer),
                    'status_text' => $this->getCustomerStatusText($customer),
                    'is_vip' => $customer->is_vip,
                    'vip_at' => $customer->vip_at,
                    'created_at' => $customer->created_at
                ];
            });

            // 计算客户统计
            $customerStats = $this->calculateCustomerStats($user->id);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $customerList,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'stats' => $customerStats
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("业务员客户API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取业务员目标设置
     */
    public function getSalesmanTargets(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $year = $request->input('year', Carbon::now()->year);

            // 获取目标设置
            $targets = DB::table('salesman_targets')
                ->where('salesman_id', $user->id)
                ->where('year', $year)
                ->first();

            if (!$targets) {
                // 创建默认目标
                $targetId = DB::table('salesman_targets')->insertGetId([
                    'salesman_id' => $user->id,
                    'year' => $year,
                    'month_vip_target' => 10,
                    'month_sales_target' => 5000,
                    'year_vip_target' => 120,
                    'year_sales_target' => 60000,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                $targets = DB::table('salesman_targets')->where('id', $targetId)->first();
            }

            // 计算目标完成进度
            $progress = $this->calculateTargetProgress($user->id, $year);

            $targetData = (array)$targets;
            $targetData['progress'] = $progress;

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $targetData
            ]);

        } catch (\Exception $e) {
            Log::error("业务员目标API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 计算佣金统计
     */
    private function calculateCommissionStats($userId)
    {
        $currentMonth = Carbon::now()->format('Y-m');

        $totalAmount = DB::table('salesman_commissions')
            ->where('salesman_id', $userId)
            ->sum('amount') ?: 0;

        $pendingAmount = DB::table('salesman_commissions')
            ->where('salesman_id', $userId)
            ->where('status', 'pending')
            ->sum('amount') ?: 0;

        $paidAmount = DB::table('salesman_commissions')
            ->where('salesman_id', $userId)
            ->where('status', 'paid')
            ->sum('amount') ?: 0;

        $monthAmount = DB::table('salesman_commissions')
            ->where('salesman_id', $userId)
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
            ->sum('amount') ?: 0;

        return [
            'total_amount' => number_format($totalAmount, 2),
            'pending_amount' => number_format($pendingAmount, 2),
            'paid_amount' => number_format($paidAmount, 2),
            'month_amount' => number_format($monthAmount, 2)
        ];
    }

    /**
     * 获取客户状态
     */
    private function getCustomerStatus($customer)
    {
        if ($customer->is_vip) {
            return 'converted';
        } elseif ($customer->created_at < Carbon::now()->subDay()) {
            return 'contacted';
        } else {
            return 'potential';
        }
    }

    /**
     * 获取客户状态文本
     */
    private function getCustomerStatusText($customer)
    {
        $status = $this->getCustomerStatus($customer);
        $statusMap = [
            'potential' => '潜在客户',
            'contacted' => '已联系',
            'converted' => '已转化'
        ];
        return $statusMap[$status] ?? '未知';
    }

    /**
     * 计算客户统计
     */
    private function calculateCustomerStats($userId)
    {
        // 潜在客户：注册但未成为VIP
        $potential = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 0)
            ->count();

        // 已联系：注册超过1天但未成为VIP
        $contacted = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 0)
            ->where('created_at', '<', Carbon::now()->subDay())
            ->count();

        // 已转化：成为VIP的客户
        $converted = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 1)
            ->count();

        return [
            'potential' => $potential,
            'contacted' => $contacted,
            'converted' => $converted,
            'total' => $potential + $contacted + $converted
        ];
    }

    /**
     * 更新业务员目标
     */
    public function updateSalesmanTargets(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $year = $request->input('year', Carbon::now()->year);
            $updateData = [];

            if ($request->has('month_vip_target')) {
                $updateData['month_vip_target'] = $request->input('month_vip_target');
            }
            if ($request->has('month_sales_target')) {
                $updateData['month_sales_target'] = $request->input('month_sales_target');
            }
            if ($request->has('year_vip_target')) {
                $updateData['year_vip_target'] = $request->input('year_vip_target');
            }
            if ($request->has('year_sales_target')) {
                $updateData['year_sales_target'] = $request->input('year_sales_target');
            }

            if (empty($updateData)) {
                return response()->json([
                    'code' => 1,
                    'message' => '没有要更新的数据',
                    'data' => null
                ]);
            }

            $updateData['updated_at'] = now();

            // 更新目标
            $affected = DB::table('salesman_targets')
                ->where('salesman_id', $user->id)
                ->where('year', $year)
                ->update($updateData);

            if ($affected === 0) {
                // 如果没有记录，创建新记录
                $updateData['salesman_id'] = $user->id;
                $updateData['year'] = $year;
                $updateData['created_at'] = now();
                DB::table('salesman_targets')->insert($updateData);
            }

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            Log::error("更新业务员目标API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取业务员团队信息
     */
    public function getSalesmanTeam(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);

            // 获取团队成员（直接推荐的业务员）
            $query = DB::table('app_users as u')
                ->leftJoin('salesmen as s', 'u.id', '=', 's.user_id')
                ->where('u.referrer_id', $user->id)
                ->where('u.is_salesman', 1)
                ->select([
                    'u.id',
                    'u.name',
                    'u.phone',
                    'u.wechat_avatar as avatar',
                    'u.created_at as join_time',
                    's.title',
                    's.status',
                    's.employee_id'
                ]);

            $total = $query->count();
            $teamMembers = $query->orderBy('u.created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get();

            // 计算团队统计
            $teamStats = $this->calculateTeamStats($user->id);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $teamMembers,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'stats' => $teamStats
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("业务员团队API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 计算目标完成进度
     */
    private function calculateTargetProgress($userId, $year)
    {
        $currentMonth = Carbon::now()->format('Y-m');

        // 本月VIP数量
        $monthVipCount = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 1)
            ->whereNotNull('vip_at')
            ->whereRaw("DATE_FORMAT(vip_at, '%Y-%m') = ?", [$currentMonth])
            ->count();

        // 本年VIP数量
        $yearVipCount = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 1)
            ->whereNotNull('vip_at')
            ->whereRaw("YEAR(vip_at) = ?", [$year])
            ->count();

        // 本月销售额（VIP数量 * 300）
        $monthSales = $monthVipCount * 300;

        // 本年销售额
        $yearSales = $yearVipCount * 300;

        return [
            'month_vip_count' => $monthVipCount,
            'year_vip_count' => $yearVipCount,
            'month_sales' => $monthSales,
            'year_sales' => $yearSales
        ];
    }

    /**
     * 邀请团队成员
     */
    public function inviteTeamMember(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $phone = $request->input('phone');
            $name = $request->input('name');

            if (!$phone) {
                return response()->json([
                    'code' => 1,
                    'message' => '手机号不能为空',
                    'data' => null
                ]);
            }

            // 检查手机号是否已存在
            $existingUser = DB::table('app_users')->where('phone', $phone)->first();
            if ($existingUser) {
                return response()->json([
                    'code' => 1,
                    'message' => '该手机号已注册',
                    'data' => null
                ]);
            }

            // 生成邀请码
            $inviteCode = 'INV' . time() . rand(1000, 9999);

            // 创建邀请记录
            $inviteId = DB::table('salesman_invites')->insertGetId([
                'inviter_id' => $user->id,
                'phone' => $phone,
                'name' => $name,
                'invite_code' => $inviteCode,
                'status' => 'pending',
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '邀请发送成功',
                'data' => [
                    'invite_id' => $inviteId,
                    'invite_code' => $inviteCode
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("邀请团队成员API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取业务员产品列表
     */
    public function getSalesmanProducts(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $category_id = $request->input('category_id');
            $keyword = $request->input('keyword');

            // 构建产品查询
            $query = DB::table('products')
                ->where('status', 'active')
                ->where('is_salesman_product', 1); // 业务员可销售的产品

            if ($category_id) {
                $query->where('category_id', $category_id);
            }

            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%");
                });
            }

            $total = $query->count();
            $products = $query->orderBy('sort', 'asc')
                ->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get();

            // 获取产品分类
            $categories = DB::table('categories')
                ->where('status', 'active')
                ->orderBy('sort', 'asc')
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $products,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'categories' => $categories
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("业务员产品API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取业务员产品详情
     */
    public function getSalesmanProductDetail(Request $request, $id)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $product = DB::table('products')
                ->where('id', $id)
                ->where('status', 'active')
                ->where('is_salesman_product', 1)
                ->first();

            if (!$product) {
                return response()->json([
                    'code' => 1,
                    'message' => '产品不存在或不可销售',
                    'data' => null
                ]);
            }

            // 获取产品图片
            $images = DB::table('product_images')
                ->where('product_id', $id)
                ->orderBy('sort', 'asc')
                ->pluck('image_url');

            // 获取产品规格
            $specifications = DB::table('product_specifications')
                ->where('product_id', $id)
                ->orderBy('sort', 'asc')
                ->get();

            $productData = (array)$product;
            $productData['images'] = $images;
            $productData['specifications'] = $specifications;

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $productData
            ]);

        } catch (\Exception $e) {
            Log::error("业务员产品详情API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取业务员培训资料
     */
    public function getSalesmanTraining(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $type = $request->input('type', 'all'); // all, video, document, course

            $query = DB::table('training_materials')
                ->where('status', 'active')
                ->where('target_audience', 'like', '%salesman%');

            if ($type !== 'all') {
                $query->where('type', $type);
            }

            $total = $query->count();
            $materials = $query->orderBy('sort', 'asc')
                ->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $materials,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("业务员培训资料API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取业务员培训资料详情
     */
    public function getSalesmanTrainingDetail(Request $request, $id)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $material = DB::table('training_materials')
                ->where('id', $id)
                ->where('status', 'active')
                ->where('target_audience', 'like', '%salesman%')
                ->first();

            if (!$material) {
                return response()->json([
                    'code' => 1,
                    'message' => '培训资料不存在',
                    'data' => null
                ]);
            }

            // 记录学习记录
            DB::table('training_records')->updateOrInsert(
                [
                    'user_id' => $user->id,
                    'material_id' => $id
                ],
                [
                    'last_viewed_at' => now(),
                    'view_count' => DB::raw('view_count + 1'),
                    'updated_at' => now()
                ]
            );

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $material
            ]);

        } catch (\Exception $e) {
            Log::error("业务员培训资料详情API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取商户工作台数据
     */
    public function getMerchantWorkspace(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            // 验证用户是否为商户
            $merchant = DB::table('merchants')
                ->where('user_id', $user->id)
                ->first();

            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }

            $today = Carbon::today()->format('Y-m-d');
            $currentMonth = Carbon::now()->format('Y-m');

            // 获取今日交易统计
            $todayStats = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->whereDate('created_at', $today)
                ->selectRaw('
                    COUNT(*) as transaction_count,
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN status = "success" THEN amount ELSE 0 END) as success_amount
                ')
                ->first();

            // 获取本月交易统计
            $monthStats = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->selectRaw('
                    COUNT(*) as transaction_count,
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN status = "success" THEN amount ELSE 0 END) as success_amount
                ')
                ->first();

            // 获取最近交易记录
            $recentTransactions = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'merchant_info' => $merchant,
                    'today_stats' => $todayStats,
                    'month_stats' => $monthStats,
                    'recent_transactions' => $recentTransactions
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("商户工作台API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取商户交易记录
     */
    public function getMerchantTransactions(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $merchant = DB::table('merchants')->where('user_id', $user->id)->first();
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $status = $request->input('status', 'all');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $query = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id);

            if ($status !== 'all') {
                $query->where('status', $status);
            }

            if ($startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            }

            if ($endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            }

            $total = $query->count();
            $transactions = $query->orderBy('created_at', 'desc')
                ->skip(($page - 1) * $limit)
                ->take($limit)
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $transactions,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("商户交易记录API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取商户交易详情
     */
    public function getMerchantTransactionDetail(Request $request, $id)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $merchant = DB::table('merchants')->where('user_id', $user->id)->first();
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }

            $transaction = DB::table('merchant_transactions')
                ->where('id', $id)
                ->where('merchant_id', $merchant->id)
                ->first();

            if (!$transaction) {
                return response()->json([
                    'code' => 1,
                    'message' => '交易记录不存在',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $transaction
            ]);

        } catch (\Exception $e) {
            Log::error("商户交易详情API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 获取商户统计数据
     */
    public function getMerchantStats(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            $merchant = DB::table('merchants')->where('user_id', $user->id)->first();
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }

            $type = $request->input('type', 'overview'); // overview, trend, detail
            $period = $request->input('period', 'month'); // day, week, month, year

            $stats = $this->calculateMerchantStats($merchant->id, $type, $period);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error("商户统计数据API错误: " . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '服务器错误',
                'data' => null
            ]);
        }
    }

    /**
     * 计算商户统计数据
     */
    private function calculateMerchantStats($merchantId, $type = 'overview', $period = 'month')
    {
        $now = Carbon::now();

        switch ($period) {
            case 'day':
                $startDate = $now->startOfDay();
                $endDate = $now->endOfDay();
                break;
            case 'week':
                $startDate = $now->startOfWeek();
                $endDate = $now->endOfWeek();
                break;
            case 'year':
                $startDate = $now->startOfYear();
                $endDate = $now->endOfYear();
                break;
            default: // month
                $startDate = $now->startOfMonth();
                $endDate = $now->endOfMonth();
                break;
        }

        $stats = DB::table('merchant_transactions')
            ->where('merchant_id', $merchantId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('
                COUNT(*) as total_transactions,
                SUM(amount) as total_amount,
                SUM(CASE WHEN status = "success" THEN amount ELSE 0 END) as success_amount,
                SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as success_count,
                AVG(amount) as avg_amount
            ')
            ->first();

        return [
            'period' => $period,
            'start_date' => $startDate->format('Y-m-d H:i:s'),
            'end_date' => $endDate->format('Y-m-d H:i:s'),
            'total_transactions' => $stats->total_transactions ?: 0,
            'total_amount' => number_format($stats->total_amount ?: 0, 2),
            'success_amount' => number_format($stats->success_amount ?: 0, 2),
            'success_count' => $stats->success_count ?: 0,
            'success_rate' => $stats->total_transactions > 0 ?
                round(($stats->success_count / $stats->total_transactions) * 100, 2) : 0,
            'avg_amount' => number_format($stats->avg_amount ?: 0, 2)
        ];
    }

    /**
     * 计算团队统计
     */
    private function calculateTeamStats($userId)
    {
        $currentMonth = Carbon::now()->format('Y-m');

        // 团队总人数
        $totalMembers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_salesman', 1)
            ->count();

        // 本月新增团队成员
        $monthNewMembers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_salesman', 1)
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
            ->count();

        // 团队本月业绩
        $monthTeamPerformance = DB::table('app_users as team')
            ->join('app_users as customers', 'customers.referrer_id', '=', 'team.id')
            ->where('team.referrer_id', $userId)
            ->where('team.is_salesman', 1)
            ->where('customers.is_vip', 1)
            ->whereNotNull('customers.vip_at')
            ->whereRaw("DATE_FORMAT(customers.vip_at, '%Y-%m') = ?", [$currentMonth])
            ->count() * 300;

        return [
            'total_members' => $totalMembers,
            'month_new_members' => $monthNewMembers,
            'month_team_performance' => number_format($monthTeamPerformance, 2)
        ];
    }
}
