<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\AppUser;
use App\Models\User;
use App\Models\Order;
use App\Models\Device;

class AppUserController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取APP用户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        \Log::info('APP用户管理API调用开始', [
            'request_params' => $request->all(),
            'user_agent' => $request->header('User-Agent'),
            'ip' => $request->ip()
        ]);

        // 使用AppUser模型查询app_users表的所有数据，与原生API保持一致
        $query = AppUser::with('branch'); // 预加载分支机构信息
        
        // 分支机构筛选 - 默认不显示分支机构用户，只有选择了分支机构才显示
        if ($request->has('branch_id') && !empty($request->branch_id)) {
            // 如果指定了分支机构ID，只显示该分支机构的用户
            $query->where('branch_id', $request->branch_id);
            \Log::info('应用分支机构筛选', ['branch_id' => $request->branch_id]);
        } else {
            // 如果没有指定分支机构，默认不显示分支机构用户（只显示总部用户）
            $query->whereNull('branch_id');
            \Log::info('应用默认筛选：显示总部用户（branch_id为NULL）');
        }
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%")
                  ->orWhere('email', 'like', "%{$keyword}%")
                  ->orWhere('wechat_nickname', 'like', "%{$keyword}%")
                  ->orWhere('id', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 角色筛选
        if ($request->has('role') && !empty($request->role)) {
            switch ($request->role) {
                case 'vip':
                    $query->where('is_vip', 1);
                    break;
                case 'engineer':
                    $query->where('is_engineer', 1);
                    break;
                case 'water_purifier_user':
                    $query->where('is_water_purifier_user', 1);
                    break;
                case 'water_purifier_agent':
                    $query->where('is_water_purifier_agent', 1);
                    break;
                case 'pay_institution':
                    $query->where('is_pay_institution', 1);
                    break;
                case 'pay_merchant':
                    $query->where('is_pay_merchant', 1);
                    break;
                case 'admin':
                    $query->where('is_admin', 1);
                    break;
            }
        }
        
        // 注册时间筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('created_at', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'id');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 获取SQL查询语句用于调试
        $sql = $query->toSql();
        $bindings = $query->getBindings();
        \Log::info('构建的SQL查询', [
            'sql' => $sql,
            'bindings' => $bindings
        ]);

        // 先获取总数
        $totalCount = $query->count();
        \Log::info('查询总数', ['total_count' => $totalCount]);

        // 分页
        $perPage = $request->input('per_page', 15);
        $users = $query->paginate($perPage);

        \Log::info('分页查询完成', [
            'current_page' => $users->currentPage(),
            'per_page' => $users->perPage(),
            'total' => $users->total(),
            'items_count' => $users->count()
        ]);
        
        // 添加角色信息和分支机构信息到每个用户
        $users->getCollection()->transform(function ($user) {
            $user->role_names = $user->getRoleNames();
            
            // 动态获取推荐人姓名
            if (!empty($user->referrer_id)) {
                $referrer = \DB::table('app_users')
                              ->select('id', 'name', 'wechat_nickname')
                              ->where('id', $user->referrer_id)
                              ->first();
                if ($referrer) {
                    $user->referrer_name = $referrer->name ?: $referrer->wechat_nickname ?: '用户'.$referrer->id;
                } else {
                    $user->referrer_name = '未知用户';
                }
            } else {
                $user->referrer_name = '点点够';
            }
            
            // 添加分支机构信息
            if ($user->branch) {
                $user->branch_name = $user->branch->name;
                $user->branch_code = $user->branch->code;
            } else {
                $user->branch_name = null;
                $user->branch_code = null;
            }
            
            return $user;
        });
        
        return $this->paginate($users);
    }

    /**
     * 创建APP用户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/|unique:users,phone',
            'email' => 'nullable|email|max:100|unique:users,email',
            'password' => 'required|string|min:6',
            'avatar' => 'nullable|string|max:200',
            'gender' => 'nullable|in:male,female,unknown',
            'birthday' => 'nullable|date',
            'address' => 'nullable|string|max:200',
            'status' => 'required|in:0,1',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $user = new User();
            $user->name = $request->name;
            $user->phone = $request->phone;
            $user->email = $request->email;
            $user->password = Hash::make($request->password);
            $user->avatar = $request->avatar;
            $user->gender = $request->gender ?? 'unknown';
            $user->birthday = $request->birthday;
            $user->address = $request->address;
            $user->status = $request->status;
            $user->remark = $request->remark;
            $user->is_admin = 0;
            $user->is_salesman = 0;
            $user->is_vip = 0;
            $user->save();
            
            return $this->success($user, 'APP用户创建成功');
        } catch (\Exception $e) {
            return $this->error('APP用户创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个APP用户详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            // 修正：查询app_users表，而不是users表
            $user = \DB::table('app_users')->where('id', $id)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 404,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            $userData = (array) $user;
            
            // 添加角色信息
            $roleNames = [];
            if ($userData['is_vip'] == 1) $roleNames[] = 'VIP会员';
            if ($userData['is_engineer'] == 1) $roleNames[] = '工程师';
            if ($userData['is_water_purifier_user'] == 1) $roleNames[] = '净水器用户';
            if ($userData['is_water_purifier_agent'] == 1) $roleNames[] = '净水器代理';
            if ($userData['is_pay_institution'] == 1) $roleNames[] = '支付机构';
            if ($userData['is_pay_merchant'] == 1) $roleNames[] = '支付商户';
            if ($userData['is_admin'] == 1) $roleNames[] = '管理员';
            if (empty($roleNames)) $roleNames[] = '普通用户';
            
            $userData['role_names'] = $roleNames;
            
            // 获取推荐人信息
            if (!empty($userData['referrer_id'])) {
                $referrer = \DB::table('app_users')
                              ->select('id', 'name', 'wechat_nickname')
                              ->where('id', $userData['referrer_id'])
                              ->first();
                if ($referrer) {
                    $userData['referrer_name'] = $referrer->name ?: $referrer->wechat_nickname ?: '用户'.$referrer->id;
                } else {
                    $userData['referrer_name'] = '未知用户';
                }
            } else {
                $userData['referrer_name'] = '点点够';
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取用户信息成功',
                'data' => $userData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取用户信息失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新APP用户
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // 修正：查询app_users表，而不是users表
            $user = \DB::table('app_users')->where('id', $id)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 404,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            // 获取请求数据
            $updateData = $request->only([
                'name', 'phone', 'email', 'status', 'gender', 'birthday',
                'wechat_nickname', 'wechat_avatar', 'wechat_gender', 'wechat_country',
                'wechat_province', 'wechat_city', 'referrer_id', 'referrer_name', 'institution_id',
                'is_vip', 'is_engineer', 'is_water_purifier_user', 'is_water_purifier_agent',
                'is_pay_institution', 'is_pay_merchant', 'is_admin', 'vip_at',
                'is_vip_paid', 'vip_paid_at'
            ]);
            
            // 如果更新手机号，检查是否与其他用户冲突
            if (!empty($updateData['phone'])) {
                $existingUser = \DB::table('app_users')
                    ->where('phone', $updateData['phone'])
                    ->where('id', '!=', $id)
                    ->first();
                
                if ($existingUser) {
                    return response()->json([
                        'code' => 400,
                        'message' => '手机号已被其他用户使用 (用户ID: ' . $existingUser->id . ', 姓名: ' . $existingUser->name . ')',
                        'data' => null
                    ]);
                }
            }
            
            // 处理生日字段格式转换
            if (isset($updateData['birthday']) && !empty($updateData['birthday'])) {
                try {
                    // 如果是ISO 8601格式，转换为MySQL date格式
                    if (strpos($updateData['birthday'], 'T') !== false) {
                        $date = new \DateTime($updateData['birthday']);
                        $updateData['birthday'] = $date->format('Y-m-d');
                    }
                    // 如果已经是YYYY-MM-DD格式，保持不变
                    elseif (preg_match('/^\d{4}-\d{2}-\d{2}$/', $updateData['birthday'])) {
                        // 验证日期有效性
                        $date = \DateTime::createFromFormat('Y-m-d', $updateData['birthday']);
                        if (!$date || $date->format('Y-m-d') !== $updateData['birthday']) {
                            throw new \Exception('无效的日期格式');
                        }
                    }
                    else {
                        throw new \Exception('不支持的日期格式');
                    }
                } catch (\Exception $e) {
                    return response()->json([
                        'code' => 400,
                        'message' => '生日格式错误: ' . $e->getMessage(),
                        'data' => null
                    ]);
                }
            }
            
            // 如果提供了密码，则加密后更新
            if ($request->has('password') && !empty($request->password)) {
                $updateData['password'] = Hash::make($request->password);
            }
            
            // 更新数据
            \DB::table('app_users')->where('id', $id)->update($updateData);
            
            return response()->json([
                'code' => 0,
                'message' => 'APP用户更新成功',
                'data' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => 'APP用户更新失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除APP用户
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->error('用户不存在', 404);
        }
        
        // 检查是否为APP用户
        if ($user->is_admin || $user->is_salesman) {
            return $this->error('该用户不是APP用户', 400);
        }
        
        // 检查是否有关联的订单
        $orderCount = Order::where('user_id', $id)->count();
        if ($orderCount > 0) {
            return $this->error('该用户有关联的订单，无法删除', 400);
        }
        
        // 检查是否有关联的设备
        $deviceCount = Device::where('user_id', $id)->count();
        if ($deviceCount > 0) {
            return $this->error('该用户有关联的设备，无法删除', 400);
        }
        
        try {
            $user->delete();
            
            return $this->success(null, 'APP用户删除成功');
        } catch (\Exception $e) {
            return $this->error('APP用户删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新APP用户状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->error('用户不存在', 404);
        }
        
        // 检查是否为APP用户
        if ($user->is_admin || $user->is_salesman) {
            return $this->error('该用户不是APP用户', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $user->status = $request->status;
            $user->save();
            
            return $this->success($user, 'APP用户状态更新成功');
        } catch (\Exception $e) {
            return $this->error('APP用户状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 重置APP用户密码
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPassword(Request $request, $id)
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->error('用户不存在', 404);
        }
        
        // 检查是否为APP用户
        if ($user->is_admin || $user->is_salesman) {
            return $this->error('该用户不是APP用户', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $user->password = Hash::make($request->password);
            $user->save();
            
            return $this->success(null, 'APP用户密码重置成功');
        } catch (\Exception $e) {
            return $this->error('APP用户密码重置失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取APP用户订单列表
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function orders(Request $request, $id)
    {
        $user = User::find($id);
        
        if (!$user) {
            return $this->error('用户不存在', 404);
        }
        
        // 检查是否为APP用户
        if ($user->is_admin || $user->is_salesman) {
            return $this->error('该用户不是APP用户', 400);
        }
        
        $query = Order::where('user_id', $id);
        
        // 订单状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 支付状态筛选
        if ($request->has('pay_status') && $request->pay_status !== '') {
            $query->where('pay_status', $request->pay_status);
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('created_at', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $orders = $query->paginate($perPage);
        
        // 加载关联数据
        $orders->load(['items.product']);
        
        return $this->paginate($orders);
    }
    
    /**
     * 获取APP用户设备列表
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function devices(Request $request, $id)
    {
        try {
            // 修正：查询app_users表，而不是users表
            $user = \DB::table('app_users')->where('id', $id)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 404,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            // 查询用户设备列表 - 使用tapp_devices表
            $query = \DB::table('tapp_devices')->where('app_user_id', $id);
            
            // 设备类型筛选
            if ($request->has('device_type') && !empty($request->device_type)) {
                $query->where('device_type', $request->device_type);
            }
            
            // 状态筛选
            if ($request->has('status') && $request->status !== '') {
                $query->where('status', $request->status);
            }
            
            // 在线状态筛选
            if ($request->has('is_online') && $request->is_online !== '') {
                $query->where('is_online', $request->is_online);
            }
            
            // 排序
            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);
            
            // 获取设备列表
            $devices = $query->get();
            
            // 转换为数组格式
            $deviceList = [];
            foreach ($devices as $device) {
                $deviceList[] = (array) $device;
            }
            
            return response()->json([
                'code' => 200,
                'message' => '获取设备列表成功',
                'data' => $deviceList,
                'total' => count($deviceList)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取设备列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 同步所有用户角色数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncAllRoles(Request $request)
    {
        try {
            $startTime = microtime(true);
            
            // 获取所有用户
            $users = \DB::table('app_users')->get();
            $total = $users->count();
            $success = 0;
            $failed = 0;
            $errors = [];
            $stats = [
                'pay_institution' => 0,
                'water_purifier_user' => 0,
                'engineer' => 0,
                'with_parent' => 0,
                'salesman' => 0
            ];
            
            foreach ($users as $user) {
                try {
                    $updated = false;
                    $userStats = $this->syncSingleUserRoles($user->id);
                    
                    if ($userStats['updated']) {
                        $updated = true;
                        if ($userStats['changes']['pay_institution']) $stats['pay_institution']++;
                        if ($userStats['changes']['water_purifier_user']) $stats['water_purifier_user']++;
                        if ($userStats['changes']['engineer']) $stats['engineer']++;
                        if ($userStats['changes']['parent_institution']) $stats['with_parent']++;
                        if ($userStats['changes']['salesman']) $stats['salesman']++;
                    }
                    
                    $success++;
                } catch (\Exception $e) {
                    $failed++;
                    $errors[] = [
                        'user_id' => $user->id,
                        'message' => $e->getMessage()
                    ];
                    
                    // 只保留前10个错误
                    if (count($errors) > 10) {
                        array_shift($errors);
                    }
                }
            }
            
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            
            return response()->json([
                'code' => 0,
                'message' => '用户角色同步完成',
                'data' => [
                    'total' => $total,
                    'success' => $success,
                    'failed' => $failed,
                    'pay_institution' => $stats['pay_institution'],
                    'water_purifier_user' => $stats['water_purifier_user'],
                    'engineer' => $stats['engineer'],
                    'with_parent' => $stats['with_parent'],
                    'salesman' => $stats['salesman'],
                    'duration' => $duration,
                    'errors' => $errors,
                    'errors_truncated' => count($errors) >= 10
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '同步失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 同步单个用户角色数据
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncUserRoles(Request $request, $id)
    {
        try {
            $user = \DB::table('app_users')->where('id', $id)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 404,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            $syncResult = $this->syncSingleUserRoles($id);
            
            if ($syncResult['updated']) {
                return response()->json([
                    'code' => 0,
                    'message' => '用户角色同步成功',
                    'data' => [
                        'sync_details' => $syncResult
                    ]
                ]);
            } else {
                return response()->json([
                    'code' => 0,
                    'message' => '用户角色无需更新',
                    'data' => [
                        'sync_details' => $syncResult
                    ]
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '同步失败: ' . $e->getMessage(),
                'data' => [
                    'error' => $e->getMessage()
                ]
            ]);
        }
    }
    
    /**
     * 同步单个用户的角色数据（内部方法）
     *
     * @param int $userId
     * @return array
     */
    private function syncSingleUserRoles($userId)
    {
        $changes = [
            'pay_institution' => false,
            'water_purifier_user' => false,
            'engineer' => false,
            'parent_institution' => false,
            'salesman' => false
        ];
        
        $results = [];
        $updated = false;
        
        // 获取用户当前信息
        $user = \DB::table('app_users')->where('id', $userId)->first();
        if (!$user) {
            throw new \Exception('用户不存在');
        }
        
        $updateData = [];
        
                 // 1. 检查支付机构角色
         try {
             $payInstitution = \DB::connection('payment_db')->table('institution')
                 ->where('app_user_id', $userId)
                 ->first();
                
            if ($payInstitution && !$user->is_pay_institution) {
                $updateData['is_pay_institution'] = 1;
                $updateData['institution_name'] = $payInstitution->name ?? '';
                $updateData['institution_number'] = $payInstitution->number ?? '';
                $changes['pay_institution'] = true;
                $updated = true;
            }
        } catch (\Exception $e) {
            // 忽略数据库连接错误
        }
        
                 // 2. 检查净水器用户角色
         try {
             $waterDevice = \DB::connection('water_db')->table('client_device')
                 ->where('app_user_id', $userId)
                 ->first();
                
            if ($waterDevice && !$user->is_water_purifier_user) {
                $updateData['is_water_purifier_user'] = 1;
                $updateData['purifier_client_device_id'] = $waterDevice->device_id ?? '';
                $updateData['purifier_client_device_name'] = $waterDevice->device_name ?? '';
                $changes['water_purifier_user'] = true;
                $updated = true;
            }
        } catch (\Exception $e) {
            // 忽略数据库连接错误
        }
        
                 // 3. 检查工程师角色
         try {
             $engineer = \DB::connection('water_db')->table('engineer')
                 ->where('app_user_id', $userId)
                 ->first();
                
            if ($engineer && !$user->is_engineer) {
                $updateData['is_engineer'] = 1;
                $updateData['engineer_id'] = $engineer->id ?? '';
                $changes['engineer'] = true;
                $updated = true;
            }
        } catch (\Exception $e) {
            // 忽略数据库连接错误
        }
        
        // 4. 确保每个用户都是业务员
        if (!$user->is_salesman) {
            $updateData['is_salesman'] = 1;
            $changes['salesman'] = true;
            $updated = true;
            
            $results['salesman'] = [
                'changed' => true,
                'action' => 'updated_flag'
            ];
        }
        
        // 5. 更新推荐人关系
        if ($user->referrer_id && $user->referrer_id > 0) {
            $referrer = \DB::table('app_users')->where('id', $user->referrer_id)->first();
            if ($referrer && $user->referrer_name !== $referrer->name) {
                $updateData['referrer_name'] = $referrer->name ?: $referrer->wechat_nickname ?: '用户'.$referrer->id;
                $changes['parent_institution'] = true;
                $updated = true;
            }
        }
        
        // 执行更新
        if ($updated && !empty($updateData)) {
            \DB::table('app_users')->where('id', $userId)->update($updateData);
        }
        
        return [
            'updated' => $updated,
            'changes' => $changes,
            'results' => $results
        ];
    }

    /**
     * 获取用户统计数据
     *
     * @param int $userId
     * @return array
     */
    private function getUserStats($userId)
    {
        // 订单统计
        $orderCount = 0;
        $orderAmount = 0;
        
        if (class_exists('App\Models\Order')) {
            $orderStats = Order::where('user_id', $userId)
                ->selectRaw('COUNT(*) as count, SUM(CASE WHEN pay_status = 1 THEN total_amount ELSE 0 END) as amount')
                ->first();
                
            $orderCount = $orderStats->count ?? 0;
            $orderAmount = $orderStats->amount ?? 0;
        }
        
        // 设备统计
        $deviceCount = 0;
        
        if (class_exists('App\Models\Device')) {
            $deviceCount = Device::where('user_id', $userId)->count();
        }
        
        // 最近登录时间
        $lastLoginTime = null;
        
        if (class_exists('App\Models\UserLogin')) {
            $lastLogin = \App\Models\UserLogin::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->first();
                
            if ($lastLogin) {
                $lastLoginTime = $lastLogin->created_at;
            }
        }
        
        return [
            'order_count' => $orderCount,
            'order_amount' => $orderAmount,
            'device_count' => $deviceCount,
            'last_login_time' => $lastLoginTime
        ];
    }
}
