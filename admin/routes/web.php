<?php

use Illuminate\Support\Facades\Route;

// 简单测试路由 - 验证Laravel是否正常工作
Route::get('/test-laravel', function () {
    return response()->json([
        'code' => 200,
        'message' => '<PERSON>vel正常工作',
        'timestamp' => now()
    ]);
});

// 测试API路由 - 验证API路径是否正常
Route::get('/api/admin/v1/test-simple', function () {
    return response()->json([
        'code' => 200,
        'message' => 'API路径测试成功',
        'timestamp' => now(),
        'source' => 'web.php'
    ]);
});

use App\Http\Controllers\PartnersController;
// use App\Http\Controllers\ProductController; // 已废弃，改用完整命名空间
use App\Http\Controllers\SmsLogController;
use App\Http\Controllers\Admin\Web\SmsManagementController;
use App\Http\Controllers\Admin\Installation\BookingController;
use App\Http\Controllers\VipDividendController;
use App\Http\Controllers\Admin\Api\V1\AuthController;
use App\Http\Controllers\Platform\MenuController;

// V1版本API路由文件已移至 RouteServiceProvider 加载

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// 临时V1 API路由 - 直接添加以绕过认证问题
Route::get('/api/admin/v1/test', function () {
    return response()->json([
        'code' => 200,
        'message' => 'V1 API工作正常（来自web.php）',
        'data' => [
            'timestamp' => now(),
            'version' => 'v1',
            'source' => 'web.php'
        ]
    ]);
});

Route::get('/api/admin/v1/app-users', function () {
    try {
        \Log::info('Web.php路由被调用：/api/admin/v1/app-users', [
            'request_params' => request()->all(),
            'user_agent' => request()->header('User-Agent'),
            'ip' => request()->ip()
        ]);

        $page = request()->get('page', 1);
        $perPage = request()->get('per_page', 10);
        $branchId = request()->get('branch_id', '');

        // 查询app_users表，而不是users表
        $query = \DB::table('app_users');

        // 分支机构筛选 - 默认显示总部用户（branch_id为NULL）
        if (request()->has('branch_id') && !empty(request()->branch_id)) {
            $query->where('branch_id', request()->branch_id);
            \Log::info('Web.php应用分支机构筛选', ['branch_id' => request()->branch_id]);
        } else {
            $query->whereNull('branch_id');
            \Log::info('Web.php应用默认筛选：显示总部用户（branch_id为NULL）');
        }

        // 获取总数
        $total = $query->count();
        \Log::info('Web.php查询总数', ['total_count' => $total]);

        // 分页查询
        $users = $query->orderBy('id', 'desc')
                       ->offset(($page - 1) * $perPage)
                       ->limit($perPage)
                       ->get();

        \Log::info('Web.php分页查询完成', [
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $total,
            'items_count' => count($users)
        ]);

        return response()->json([
            'code' => 200,
            'message' => '获取APP用户列表成功',
            'data' => [
                'data' => $users,
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    } catch (\Exception $e) {
        \Log::error('Web.php获取APP用户列表失败', ['error' => $e->getMessage()]);
        return response()->json([
            'code' => 500,
            'message' => '获取APP用户列表失败: ' . $e->getMessage(),
            'data' => null
        ]);
    }
});

// V1版本API路由已移至 RouteServiceProvider，不在web.php中定义

// 临时修复：分支机构菜单树API - 绕过api中间件组的认证问题
Route::get('/api/admin/v1/branch-menus/tree/{branchId?}', function ($branchId = null) {
    try {
        $controller = new \App\Http\Controllers\Admin\Api\V1\BranchMenuController();
        return $controller->getBranchMenus($branchId);
    } catch (\Exception $e) {
        return response()->json([
            'code' => 500,
            'message' => '获取菜单失败: ' . $e->getMessage(),
            'data' => null
        ]);
    }
});

// 获取CSRF令牌的端点，用于API请求
Route::get('/csrf-token', function () {
    return response()->json([
        'token' => csrf_token()
    ]);
});

// 菜单获取API已移至api.php路由文件中，使用正确的控制器处理

// 商品管理 - 已废弃，使用API路由
// Route::resource('products', ProductController::class);

// 短信日志管理
Route::get('/sms-logs', [SmsLogController::class, 'index'])->name('sms.logs.index');
Route::get('/sms-logs/{id}', [SmsLogController::class, 'show'])->name('sms.logs.show');

// 特殊处理业务员相关路由刷新
Route::get('/users/salesmen', function () {
    return view('app');
})->name('salesmen.index');

Route::get('/users/salesmen/{id}', function () {
    return view('app');
})->name('salesmen.show');

Route::get('/users/salesman-stats', function () {
    return view('app');
})->name('salesman-stats.index');

// 兼容旧版设备管理URL
Route::group(['prefix' => 'Tapp/admin/public', 'middleware' => 'auth'], function() {
    Route::resource('devices', \App\Http\Controllers\Admin\DeviceController::class)->names('legacy.devices');
});

// 合作伙伴路由
Route::group(['prefix' => 'admin', 'middleware' => 'auth'], function() {
    Route::get('partners', [PartnersController::class, 'index'])->name('admin.partners');
    Route::resource('banners', \App\Http\Controllers\Admin\BannerController::class)->names('admin.banners');
    // 设备管理路由
    Route::resource('devices', \App\Http\Controllers\Admin\DeviceController::class)->names('admin.devices');
    // 点点够设备管理路由
    Route::resource('tapp-devices', \App\Http\Controllers\Admin\Web\TappDeviceController::class)->names('admin.tapp_devices');
    // 设备管理API路由
    Route::prefix('api/devices')->group(function() {
        Route::get('/', [\App\Http\Controllers\Admin\DeviceController::class, 'apiIndex'])->name('admin.devices.api.index');
        Route::post('/', [\App\Http\Controllers\Admin\DeviceController::class, 'apiStore'])->name('admin.devices.api.store');
        Route::get('/{id}', [\App\Http\Controllers\Admin\DeviceController::class, 'apiShow'])->name('admin.devices.api.show');
        Route::put('/{id}', [\App\Http\Controllers\Admin\DeviceController::class, 'apiUpdate'])->name('admin.devices.api.update');
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\DeviceController::class, 'apiUpdateStatus'])->name('admin.devices.api.update_status');
        Route::get('/clients/list', [\App\Http\Controllers\Admin\DeviceController::class, 'apiClients'])->name('admin.devices.api.clients');
    });
    // 点点够设备管理API路由 - 使用V1 API风格
    Route::prefix('api/admin/v1/tapp-devices')->group(function() {
        Route::get('/', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiIndex'])->name('api.admin.v1.tapp-devices.index');
        Route::get('/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'show'])->name('api.admin.v1.tapp-devices.show');
        Route::put('/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'update'])->name('api.admin.v1.tapp-devices.update');
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'destroy'])->name('api.admin.v1.tapp-devices.destroy');
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'updateStatus'])->name('api.admin.v1.tapp-devices.status');
        Route::post('/sync', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'syncTappDevices'])->name('api.admin.v1.tapp-devices.sync');
        Route::get('/stats', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getStats'])->name('api.admin.v1.tapp-devices.stats');
        Route::post('/batch', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'batchOperate'])->name('api.admin.v1.tapp-devices.batch');
        Route::get('/export', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'export'])->name('api.admin.v1.tapp-devices.export');
        Route::get('/{id}/water-logs', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getWaterLogs'])->name('api.admin.v1.tapp-devices.water-logs');
        Route::get('/{id}/filters', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getFilters'])->name('api.admin.v1.tapp-devices.filters');
        Route::put('/{id}/filters', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'updateFilters'])->name('api.admin.v1.tapp-devices.filters.update');
        Route::post('/{id}/reset-filter', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'resetFilter'])->name('api.admin.v1.tapp-devices.reset-filter');
        Route::get('/{id}/online-status', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getOnlineStatus'])->name('api.admin.v1.tapp-devices.online-status');
        Route::post('/{id}/control', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'control'])->name('api.admin.v1.tapp-devices.control');
        Route::get('/{id}/alerts', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getAlerts'])->name('api.admin.v1.tapp-devices.alerts');
        
        // 辅助数据API
        Route::get('/app-users/list', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getAppUsers'])->name('api.admin.v1.tapp-devices.app-users');
        Route::get('/dealers/list', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getDealers'])->name('api.admin.v1.tapp-devices.dealers');
        Route::get('/clients/list', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getClients'])->name('api.admin.v1.tapp-devices.clients');
    });

    // 商城订单管理路由
    Route::resource('mall/orders', \App\Http\Controllers\Admin\Web\MallOrderController::class)->names('admin.mall_orders');

    // 商城订单API路由
    Route::prefix('api/mall/orders')->group(function() {
        Route::get('/', [\App\Http\Controllers\Admin\Web\MallOrderController::class, 'apiIndex'])->name('admin.mall_orders.api.index');
        Route::get('/{id}', [\App\Http\Controllers\Admin\Web\MallOrderController::class, 'apiShow'])->name('admin.mall_orders.api.show');
    });

    // 安装预约API路由
    Route::prefix('api/installation-bookings')->group(function() {
        Route::get('/', [BookingController::class, 'index'])->name('admin.installation_bookings.api.index');
        Route::post('/', [BookingController::class, 'store'])->name('admin.installation_bookings.api.store');
        Route::get('/{id}', [BookingController::class, 'show'])->name('admin.installation_bookings.api.show');
        Route::put('/{id}', [BookingController::class, 'update'])->name('admin.installation_bookings.api.update');
        Route::delete('/{id}', [BookingController::class, 'destroy'])->name('admin.installation_bookings.api.destroy');
    });
});

// Tapp设备相关路由
Route::group(['middleware' => ['web', 'auth']], function () {
    // API接口
    Route::group(['prefix' => 'api/tapp-devices'], function () {
            Route::get('/', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiIndex'])->name('api.tapp-devices.index');
    Route::get('/app-users/list', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getAppUsers'])->name('api.tapp-devices.app-users');
    Route::get('/stats', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getStats'])->name('api.tapp-devices.stats');
    Route::get('/export', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'export'])->name('api.tapp-devices.export');
    Route::post('/batch', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'batchOperate'])->name('api.tapp-devices.batch');
    Route::post('/sync', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'syncData'])->name('api.tapp-devices.sync');
    Route::get('/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiShow'])->name('api.tapp-devices.show');
    Route::put('/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiUpdate'])->name('api.tapp-devices.update');
    Route::delete('/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'destroy'])->name('api.tapp-devices.destroy');
    Route::put('/{id}/status', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiSetStatus'])->name('api.tapp-devices.status');
    Route::get('/{id}/water-logs', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getWaterLogs'])->name('api.tapp-devices.water-logs');
    Route::get('/{id}/filters', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getFilters'])->name('api.tapp-devices.filters');
    Route::put('/{id}/filters', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'updateFilters'])->name('api.tapp-devices.filters.update');
    Route::post('/{id}/filters/reset', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'resetFilter'])->name('api.tapp-devices.filters.reset');
    Route::get('/{id}/online-status', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getOnlineStatus'])->name('api.tapp-devices.online-status');
    Route::post('/{id}/control', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'control'])->name('api.tapp-devices.control');
    Route::get('/{id}/alerts', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getAlerts'])->name('api.tapp-devices.alerts');
    });

    // 页面路由
    Route::get('/tapp-devices', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'index'])->name('tapp-devices.index');
    Route::get('/tapp-devices/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'show'])->name('tapp-devices.show');
    Route::get('/tapp-devices/{id}/edit', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'edit'])->name('tapp-devices.edit');
});

// 短信管理模块路由
Route::group(['prefix' => 'system', 'middleware' => ['auth']], function () {
    Route::get('/logs', [SmsManagementController::class, 'logs'])->name('sms.logs');
    Route::get('/logs/{id}', [SmsManagementController::class, 'logDetail'])->name('sms.logs.detail');
    Route::get('/codes', [SmsManagementController::class, 'codes'])->name('sms.codes');
    Route::get('/statistics', [SmsManagementController::class, 'statistics'])->name('sms.statistics');
    Route::get('/api/logs', [SmsManagementController::class, 'apiLogs'])->name('sms.api.logs');
    Route::get('/api/statistics', [SmsManagementController::class, 'apiStatistics'])->name('sms.api.statistics');
});

// 添加显式的短信管理前端路由，确保SPA应用正确处理
Route::get('/system/sms-logs', function () {
    return view('app');
});

Route::get('/system/sms-statistics', function () {
    return view('app');
});

Route::get('/system/sms-codes', function () {
    return view('app');
});

// 确保新路由格式也能正常工作
Route::get('/system/sms/logs', function () {
    return view('app');
});

Route::get('/system/sms/statistics', function () {
    return view('app');
});

Route::get('/system/sms/codes', function () {
    return view('app');
});

// 业务员管理路由
Route::group(['middleware' => ['web', 'auth'], 'prefix' => 'admin'], function () {
    // 业务员管理
    Route::prefix('salesmen')->name('admin.salesmen.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Web\SalesmanController::class, 'index'])->name('index');
        Route::get('/create', [\App\Http\Controllers\Admin\Web\SalesmanController::class, 'create'])->name('create');
        Route::post('/store', [\App\Http\Controllers\Admin\Web\SalesmanController::class, 'store'])->name('store');
        Route::get('/{salesman}', [\App\Http\Controllers\Admin\Web\SalesmanController::class, 'show'])->name('show');
        Route::get('/{salesman}/edit', [\App\Http\Controllers\Admin\Web\SalesmanController::class, 'edit'])->name('edit');
        Route::put('/{salesman}', [\App\Http\Controllers\Admin\Web\SalesmanController::class, 'update'])->name('update');
        Route::delete('/{salesman}', [\App\Http\Controllers\Admin\Web\SalesmanController::class, 'destroy'])->name('destroy');

        // 业务员目标管理路由
        Route::get('/{salesman}/targets', [\App\Http\Controllers\Admin\Web\SalesmanTargetController::class, 'index'])->name('targets.index');
        Route::get('/{salesman}/targets/create', [\App\Http\Controllers\Admin\Web\SalesmanTargetController::class, 'create'])->name('targets.create');
        Route::post('/{salesman}/targets', [\App\Http\Controllers\Admin\Web\SalesmanTargetController::class, 'store'])->name('targets.store');
        Route::get('/{salesman}/targets/{target}/edit', [\App\Http\Controllers\Admin\Web\SalesmanTargetController::class, 'edit'])->name('targets.edit');
        Route::put('/{salesman}/targets/{target}', [\App\Http\Controllers\Admin\Web\SalesmanTargetController::class, 'update'])->name('targets.update');
        Route::delete('/{salesman}/targets/{target}', [\App\Http\Controllers\Admin\Web\SalesmanTargetController::class, 'destroy'])->name('targets.destroy');

        // 销售记录管理
        Route::get('/{salesman}/sales', [\App\Http\Controllers\Admin\Web\SalesmanSaleController::class, 'index'])->name('sales.index');
        Route::get('/{salesman}/sales/create', [\App\Http\Controllers\Admin\Web\SalesmanSaleController::class, 'create'])->name('sales.create');
        Route::post('/{salesman}/sales', [\App\Http\Controllers\Admin\Web\SalesmanSaleController::class, 'store'])->name('sales.store');

        // 提成管理
        Route::get('/{salesman}/commissions', [\App\Http\Controllers\Admin\Web\SalesmanCommissionController::class, 'index'])->name('commissions.index');
        Route::get('/{salesman}/commissions/create', [\App\Http\Controllers\Admin\Web\SalesmanCommissionController::class, 'create'])->name('commissions.create');
        Route::post('/{salesman}/commissions', [\App\Http\Controllers\Admin\Web\SalesmanCommissionController::class, 'store'])->name('commissions.store');

        // 客户管理
        Route::get('/{salesman}/customers', [\App\Http\Controllers\Admin\Web\SalesmanCustomerController::class, 'index'])->name('customers.index');
        Route::get('/{salesman}/customers/create', [\App\Http\Controllers\Admin\Web\SalesmanCustomerController::class, 'create'])->name('customers.create');
        Route::post('/{salesman}/customers', [\App\Http\Controllers\Admin\Web\SalesmanCustomerController::class, 'store'])->name('customers.store');
    });

    // 业务员统计
    Route::prefix('salesman-stats')->name('admin.salesman-stats.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Web\SalesmanStatController::class, 'index'])->name('index');
        Route::get('/{salesman}', [\App\Http\Controllers\Admin\Web\SalesmanStatController::class, 'show'])->name('show');
    });

    // 业务员API路由
    Route::prefix('api')->group(function () {
        // 业务员API
        Route::get('/salesmen', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'index']);
        Route::post('/salesmen', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'store']);
        Route::get('/salesmen/available-users', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'getAvailableUsers']);
        Route::get('/salesmen/managers', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'getManagers']);
        Route::get('/salesmen/debug/stats', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'getDebugStats']);
        Route::get('/salesmen/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'show']);
        Route::get('/salesmen/{id}/edit', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'edit']);
        Route::put('/salesmen/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'update']);
        Route::delete('/salesmen/{id}', [\App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'destroy']);

        // 点点够设备API（确保前端API工作正常）
        Route::prefix('admin/tapp-devices')->group(function () {
                Route::get('/', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiIndex']);
    Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceController::class, 'getAppUsers']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiShow']);
    Route::put('/{id}', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiUpdate']);
    Route::post('/sync', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'syncData']);
    Route::put('/{id}/status', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'apiSetStatus']);
        });

        // APP用户同步到业务员
        Route::post('/app-users/sync-to-salesmen', [\App\Http\Controllers\Admin\Api\AppUserController::class, 'syncToSalesmen']);

        // APP用户设备管理
        Route::prefix('app-users')->group(function () {
            Route::get('/{userId}/devices', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'index']);
            Route::post('/{userId}/devices', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'store']);
        });

        // 用户设备管理API
        Route::prefix('user-devices')->group(function () {
            Route::get('/{id}', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'show']);
            Route::put('/{id}', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'update']);
            Route::delete('/{id}', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'destroy']);
            Route::post('/{id}/main', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'setMainDevice']);
        });

        // 调试API
        Route::get('/debug/query-db', [\App\Http\Controllers\Admin\Api\DebugController::class, 'queryDb']);

        // 销售记录API - 暂时注释掉，控制器不存在
        // Route::get('/salesman-sales', [\App\Http\Controllers\Admin\Api\SalesmanSaleController::class, 'index']);
        // Route::post('/salesman-sales', [\App\Http\Controllers\Admin\Api\SalesmanSaleController::class, 'store']);
        // Route::get('/salesman-sales/{id}', [\App\Http\Controllers\Admin\Api\SalesmanSaleController::class, 'show']);
        // Route::put('/salesman-sales/{id}', [\App\Http\Controllers\Admin\Api\SalesmanSaleController::class, 'update']);
        // Route::delete('/salesman-sales/{id}', [\App\Http\Controllers\Admin\Api\SalesmanSaleController::class, 'destroy']);

        // 佣金API - 暂时注释掉，控制器不存在
        // Route::get('/salesman-commissions', [\App\Http\Controllers\Admin\Api\SalesmanCommissionController::class, 'index']);
        // Route::post('/salesman-commissions', [\App\Http\Controllers\Admin\Api\SalesmanCommissionController::class, 'store']);
        // Route::get('/salesman-commissions/{id}', [\App\Http\Controllers\Admin\Api\SalesmanCommissionController::class, 'show']);
        // Route::put('/salesman-commissions/{id}', [\App\Http\Controllers\Admin\Api\SalesmanCommissionController::class, 'update']);
        // Route::delete('/salesman-commissions/{id}', [\App\Http\Controllers\Admin\Api\SalesmanCommissionController::class, 'destroy']);
        // Route::post('/salesman-commissions/{id}/pay', [\App\Http\Controllers\Admin\Api\SalesmanCommissionController::class, 'pay']);

        // 目标API - 暂时注释掉，控制器不存在
        // Route::get('/salesman-targets', [\App\Http\Controllers\Admin\Api\SalesmanTargetController::class, 'index']);
        // Route::post('/salesman-targets', [\App\Http\Controllers\Admin\Api\SalesmanTargetController::class, 'store']);
        // Route::get('/salesman-targets/{id}', [\App\Http\Controllers\Admin\Api\SalesmanTargetController::class, 'show']);
        // Route::put('/salesman-targets/{id}', [\App\Http\Controllers\Admin\Api\SalesmanTargetController::class, 'update']);
        // Route::delete('/salesman-targets/{id}', [\App\Http\Controllers\Admin\Api\SalesmanTargetController::class, 'destroy']);
        // Route::post('/salesman-targets/{id}/complete', [\App\Http\Controllers\Admin\Api\SalesmanTargetController::class, 'complete']);
        // Route::post('/salesman-targets/{id}/cancel', [\App\Http\Controllers\Admin\Api\SalesmanTargetController::class, 'cancel']);

        // 客户API - 暂时注释掉，控制器不存在
        // Route::get('/salesman-customers', [\App\Http\Controllers\Admin\Api\SalesmanCustomerController::class, 'index']);
        // Route::post('/salesman-customers', [\App\Http\Controllers\Admin\Api\SalesmanCustomerController::class, 'store']);
        // Route::get('/salesman-customers/{id}', [\App\Http\Controllers\Admin\Api\SalesmanCustomerController::class, 'show']);
        // Route::put('/salesman-customers/{id}', [\App\Http\Controllers\Admin\Api\SalesmanCustomerController::class, 'update']);
        // Route::delete('/salesman-customers/{id}', [\App\Http\Controllers\Admin\Api\SalesmanCustomerController::class, 'destroy']);

        // 业务员统计API - 暂时注释掉，控制器不存在
        // Route::get('/salesman-stats', [\App\Http\Controllers\Admin\Api\SalesmanStatController::class, 'index']);
        // Route::get('/salesman-stats/{id}', [\App\Http\Controllers\Admin\Api\SalesmanStatController::class, 'show']);
        // Route::get('/salesman-stats/rankings/quantity', [\App\Http\Controllers\Admin\Api\SalesmanStatController::class, 'getQuantityRanking']);
        // Route::get('/salesman-stats/rankings/amount', [\App\Http\Controllers\Admin\Api\SalesmanStatController::class, 'getAmountRanking']);
        // Route::get('/salesman-stats/rankings/commission', [\App\Http\Controllers\Admin\Api\SalesmanStatController::class, 'getCommissionRanking']);
    });
});

// 更新所有业务员目标完成情况
Route::post('/salesmen/targets/update-achievements', [\App\Http\Controllers\Admin\Web\SalesmanTargetController::class, 'updateAchievements'])->name('admin.salesmen.targets.update-achievements');

// 修复重复路径前缀问题
Route::group(['prefix' => 'Tapp/admin/public'], function() {
    // 处理短信模块的路由 - 修复路径重复问题
    Route::get('/system/sms/logs', function () {
        return view('app');
    });

    Route::get('/system/sms/statistics', function () {
        return view('app');
    });

    Route::get('/system/sms/codes', function () {
        return view('app');
    });

    // 处理旧式短信路径格式 (sms- 替换为 sms/)
    Route::get('/system/sms-logs', function () {
        return view('app');
    });

    Route::get('/system/sms-statistics', function () {
        return view('app');
    });

    Route::get('/system/sms-codes', function () {
        return view('app');
    });

    // 安装预约路由
    Route::get('/installation/booking', function () {
        return view('app');
    });

    Route::get('/installation/statistics', function () {
        return view('app');
    });

    // 处理双重前缀路径 - 例如 /Tapp/admin/public/Tapp/admin/public/system/sms/...
    Route::get('/Tapp/admin/public/system/sms/{path}', function ($path) {
        return view('app');
    })->where('path', '.*');

    // 处理其他所有重复路径前缀的情况 - 为保险起见保留
    Route::get('/{any?}', function ($any = null) {
        if (!$any) {
            return redirect('/Tapp/admin/public/');
        }
        return redirect('/Tapp/admin/public/' . $any);
    })->where('any', '.*');

    // 添加VIP分红相关API到公共访问路径
    Route::get('/vip-dividends/api/vip-users', [\App\Http\Controllers\VipDividendController::class, 'apiVipUsers']);
    Route::get('/vip-dividends/api/vip-users/{id}', [\App\Http\Controllers\VipDividendController::class, 'apiVipUserDetail']);
    Route::get('/vip-dividends/api/vip-dividends', [\App\Http\Controllers\VipDividendController::class, 'apiVipDividends']);
    Route::get('/vip-dividends/api/vip-team', [\App\Http\Controllers\VipDividendController::class, 'apiVipTeam']);

    // 添加VIP用户详情和VIP分红列表页面路由
    Route::get('/vip-users/show/{id}', function ($id) {
        return view('app');
    });

    Route::get('/vip-dividends', function () {
        return view('app');
    });

    // 添加/users/vip-dividends路由
    Route::get('/users/vip-dividends', function () {
        return view('app');
    });
});

// VIP分红管理
Route::prefix('vip-dividends')->name('vip_dividends.')->middleware(['auth'])->group(function () {
    Route::get('/', [\App\Http\Controllers\VipDividendController::class, 'index'])->name('index');
    Route::get('/show/{id}', [\App\Http\Controllers\VipDividendController::class, 'show'])->name('show');
    Route::get('/calculate', [\App\Http\Controllers\VipDividendController::class, 'calculate'])->name('calculate');
    Route::post('/calculate', [\App\Http\Controllers\VipDividendController::class, 'doCalculate'])->name('do_calculate');
    Route::post('/settle', [\App\Http\Controllers\VipDividendController::class, 'settle'])->name('settle');
    Route::get('/export', [\App\Http\Controllers\VipDividendController::class, 'export'])->name('export');
    Route::post('export-vip-dividends', [\App\Http\Controllers\VipDividendController::class, 'export']);
    Route::post('settle-vip-dividends', [\App\Http\Controllers\VipDividendController::class, 'settle']);
    Route::get('api/vip-users', [\App\Http\Controllers\VipDividendController::class, 'apiVipUsers']);
    Route::get('api/vip-users/{id}', [\App\Http\Controllers\VipDividendController::class, 'apiVipUserDetail']);
    Route::get('api/vip-dividends', [\App\Http\Controllers\VipDividendController::class, 'apiVipDividends']);
    Route::get('api/vip-team', [\App\Http\Controllers\VipDividendController::class, 'apiVipTeam']);
});

// Public access paths
Route::prefix('admin/public')->group(function () {
    Route::get('/', function () {
        return redirect('/admin');
    });

    // ... existing code ...

    // VIP分红相关API
    Route::prefix('vip-dividends')->group(function () {
        Route::get('/api/vip-users', [App\Http\Controllers\VipDividendController::class, 'apiVipUsers']);
        Route::get('/api/vip-users/{id}', [App\Http\Controllers\VipDividendController::class, 'apiVipUserDetail']);
        Route::get('/api/vip-dividends', [App\Http\Controllers\VipDividendController::class, 'apiVipDividends']);
        Route::get('/api/vip-team', [App\Http\Controllers\VipDividendController::class, 'apiVipTeam']);
    });

    // 添加VIP用户详情和VIP分红列表页面路由
    Route::get('/vip-users/show/{id}', function ($id) {
        return view('app');
    });

    Route::get('/vip-dividends', function () {
        return view('app');
    });

    // 添加/users/vip-dividends路由
    Route::get('/users/vip-dividends', function () {
        return view('app');
    });
});

// 添加根路径下的VIP分红相关路由
Route::get('/vip-dividends', function () {
    return view('app');
});

Route::get('/vip-users/show/{id}', function ($id) {
    return view('app');
});

Route::get('/vip-dividends/api/vip-users', [\App\Http\Controllers\VipDividendController::class, 'apiVipUsers']);
Route::get('/vip-dividends/api/vip-users/{id}', [\App\Http\Controllers\VipDividendController::class, 'apiVipUserDetail']);
Route::get('/vip-dividends/api/vip-dividends', [\App\Http\Controllers\VipDividendController::class, 'apiVipDividends']);
Route::get('/vip-dividends/api/vip-team', [\App\Http\Controllers\VipDividendController::class, 'apiVipTeam']);

// 添加/users/vip-dividends路由
Route::get('/users/vip-dividends', function () {
    return view('app');
});

// 添加手机端API重定向路由
Route::get('/tabbar.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
Route::get('/home.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
Route::get('/banners.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
Route::get('/categories.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
Route::get('/products.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
Route::get('/product_detail.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
Route::get('/water_points.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
Route::get('/water_point_detail.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
Route::get('/nearby_water_points.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);

/*
|--------------------------------------------------------------------------
| 微信登录回调处理API - Web路由
|--------------------------------------------------------------------------
|
| 以下路由处理微信登录回调，支持多种路径格式和请求方法
| 所有路由都指向同一个控制器方法，确保一致性
| 所有路由都显式定义为公开访问，不需要任何中间件
|
*/

// 标准RESTful API路径 - 主要路径
Route::match(['get', 'post', 'options'], '/api/auth/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.api.auth.wechat.callback');

// 简化的RESTful API路径
Route::match(['get', 'post', 'options'], '/api/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.api.wechat.callback');

// 不带api前缀的RESTful路径
Route::match(['get', 'post', 'options'], '/auth/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.auth.wechat.callback');

Route::match(['get', 'post', 'options'], '/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.wechat.callback');

// 兼容旧版API路径
Route::match(['get', 'post', 'options'], '/wechat/login_callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.wechat.login_callback');

Route::match(['get', 'post', 'options'], '/api/wechat/login_callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.api.wechat.login_callback');

Route::match(['get', 'post', 'options'], '/wechat_login_callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.wechat_login_callback');

Route::match(['get', 'post', 'options'], '/api/wechat_login_callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.api.wechat_login_callback');

// 微信登录和绑定二维码扫码处理
Route::get('/wechat/login', [\App\Http\Controllers\WechatLoginController::class, 'handleLogin'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('wechat.login');

Route::get('/wechat/bind', [\App\Http\Controllers\WechatLoginController::class, 'handleBind'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('wechat.bind');

// HTML页面路径
Route::match(['get', 'post', 'options'], '/wechat_login_callback.html', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.wechat_login_callback.html');

Route::match(['get', 'post', 'options'], '/wechat-callback.html', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.wechat-callback.html');

// 直接callback路径
Route::match(['get', 'post', 'options'], '/callback', [\App\Http\Controllers\Api\WechatController::class, 'handleLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('web.callback');

// 用户相关API重定向
Route::prefix('user')->group(function () {
    Route::get('/login.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
    Route::get('/login_by_sms.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
    Route::get('/register.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
    Route::get('/info.php', [\App\Http\Controllers\RedirectController::class, 'redirectApi']);
});

// V1菜单API路由 - 连接到真正的控制器
Route::prefix('api/admin/v1')->group(function () {
    Route::get('menus', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'index']);
    Route::post('menus', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'store']);
    Route::get('menus/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'show']);
    Route::put('menus/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'update']);
    Route::delete('menus/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'destroy']);
});

// Platform菜单管理路由 - 复制自w.itapgo.com
Route::prefix('platform')->group(function () {
    // 具体路径必须放在通配符路径之前
    Route::get('menu/sync', [MenuController::class, 'sync'])->name('platform.menu.sync');
    Route::get('menu/logs', [MenuController::class, 'getLogs'])->name('platform.menu.logs');
    Route::get('menu/groups', [MenuController::class, 'getGroups'])->name('platform.menu.groups');
    Route::get('menu/templates', [MenuController::class, 'getTemplates'])->name('platform.menu.templates');
    Route::post('menu/publish', [MenuController::class, 'publish'])->name('platform.menu.publish');
    Route::post('menu/delete', [MenuController::class, 'deleteMenu'])->name('platform.menu.delete');
    Route::post('menu/copy', [MenuController::class, 'copyMenu'])->name('platform.menu.copy');
    Route::post('menu/display', [MenuController::class, 'setDisplay'])->name('platform.menu.display');
    Route::post('menu/apply-template', [MenuController::class, 'applyTemplate'])->name('platform.menu.apply-template');
    
    // 通配符路径放在最后
    Route::get('menu', [MenuController::class, 'index'])->name('platform.menu.index');
    Route::post('menu', [MenuController::class, 'store'])->name('platform.menu.store');
    Route::get('menu/{id}', [MenuController::class, 'show'])->name('platform.menu.show');
    Route::put('menu/{id}', [MenuController::class, 'update'])->name('platform.menu.update');
    Route::delete('menu/{id}', [MenuController::class, 'destroy'])->name('platform.menu.destroy');
});

// 微信第三方平台事件推送接收（公开路由，无需认证）
Route::any('/wechat-third-party/{id}/event-push', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'handleEventPush'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf']);

// 微信第三方平台消息事件接收（公开路由，无需认证）
Route::any('/wechat-third-party/{id}/message/{appid}', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'handleMessageEvent'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf']);

// 微信第三方平台事件接收测试路由（公开路由，无需认证）
Route::any('/wechat-third-party/{id}/test-event', [\App\Http\Controllers\Admin\Api\V1\WechatThirdPartyPlatformController::class, 'testEventReceiver'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf']);

// Vue SPA应用入口 - 所有前端路由均由Vue Router处理
// 此路由必须放在最后，用于捕获所有未被上面路由捕获的请求
// 但是要排除API路径，避免API请求返回HTML
Route::get('/{any?}', function () {
    return view('app');
})->where('any', '^(?!api/|admin/api/).*');

/*
|--------------------------------------------------------------------------
| 完全公开的点点够设备API路由 - 在web路由中定义，绕过LegacyTokenMiddleware
|--------------------------------------------------------------------------
*/

// 点点够设备API - 完全公开，不使用任何中间件
Route::group(['prefix' => 'api/Tapp/admin/public/api/tapp-devices'], function () {
    Route::get('/', function() {
        try {
            $controller = new \App\Http\Controllers\Admin\Api\TappDeviceApiController();
            return $controller->index(request());
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取设备列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    Route::get('/{id}', function($id) {
        try {
            $controller = new \App\Http\Controllers\Admin\Api\TappDeviceApiController();
            return $controller->show($id);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取设备详情失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    })->where('id', '[0-9]+');
    
    Route::get('/app-users/list', function() {
        try {
            $controller = new \App\Http\Controllers\Admin\Api\TappDeviceApiController();
            return $controller->getAppUsers(request());
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取APP用户列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    Route::post('/sync', function() {
        try {
            $controller = new \App\Http\Controllers\Admin\Api\TappDeviceApiController();
            return $controller->syncData(request());
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '同步数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
});

// 兼容版本的点点够设备API - 完全公开
Route::group(['prefix' => 'api/api/tapp-devices'], function () {
    Route::get('/', function() {
        try {
            $controller = new \App\Http\Controllers\Admin\Api\TappDeviceApiController();
            return $controller->index(request());
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取设备列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
});

// V1版本的点点够设备API - 完全公开
Route::group(['prefix' => 'api/api/admin/v1/tapp-devices'], function () {
    Route::get('/', function() {
        try {
            $controller = new \App\Http\Controllers\Admin\Api\TappDeviceApiController();
            return $controller->index(request());
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取设备列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
});

// 安装管理API - 完全公开
Route::group(['prefix' => 'api/admin/installation'], function () {
    Route::get('/booking', function() {
        try {
            $controller = new \App\Http\Controllers\Admin\Installation\BookingController();
            return $controller->index(request());
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取安装预约列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    Route::get('/available-engineers', function() {
        try {
            $controller = new \App\Http\Controllers\Admin\Installation\BookingController();
            return $controller->getAvailableEngineers(request());
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取可用工程师列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    Route::get('/statistics', function() {
        try {
            $controller = new \App\Http\Controllers\Admin\Installation\StatisticsController();
            return $controller->index(request());
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取安装统计失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
});
