<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// 引入简单测试路由
if (file_exists(__DIR__ . '/test_simple.php')) {
    require __DIR__ . '/test_simple.php';
}
use App\Http\Controllers\Mobile\Api\V1\WaterPointController;
use App\Http\Controllers\Admin\Api\SmsController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\SettingsController;
use App\Http\Controllers\Api\NavController;
use App\Http\Controllers\Admin\Web\AdminAuthController;
use App\Http\Controllers\Api\HomeNavController;
use App\Http\Controllers\Api\TabbarNavController;

// 添加缺少的Controller导入
use App\Http\Controllers\Admin\Web\SmsManagementController;
use App\Http\Controllers\Admin\AdminSmsLogController;
use App\Http\Controllers\Admin\Web\UserProfileController;
use App\Http\Controllers\Admin\Web\AdminMenuController;
use App\Http\Controllers\Admin\Web\AdminUserController;
use App\Http\Controllers\Admin\Web\RoleController;
use App\Http\Controllers\Admin\Web\PermissionController;
use App\Http\Controllers\Admin\Web\PartnerController;
use App\Http\Controllers\Admin\Web\ProductCategoryController;
use App\Http\Controllers\Admin\Api\AppUserController as AdminApiAppUserController;
use App\Http\Controllers\Admin\AppUserController as AdminAppUserController;
use App\Http\Controllers\Admin\Web\OrderController;
use App\Http\Controllers\Admin\Web\OrderRefundController;
use App\Http\Controllers\Admin\Web\LogisticsController;
use App\Http\Controllers\Admin\Web\MallController;
use App\Http\Controllers\Admin\Web\ProductController;
use App\Http\Controllers\Admin\Web\CategoryController;
use App\Http\Controllers\Admin\Web\BannerController;
use App\Http\Controllers\Admin\Web\DeviceController;
use App\Http\Controllers\Admin\Web\MerchantController;
use App\Http\Controllers\Admin\Web\PaymentMethodController;
use App\Http\Controllers\Admin\Web\PaymentOrderController;
use App\Http\Controllers\Admin\Web\RefundController;
use App\Http\Controllers\Admin\Web\TransactionController;
use App\Http\Controllers\Admin\Web\StatisticsController;
use App\Http\Controllers\Admin\Web\NotificationController;
use App\Http\Controllers\Admin\Api\V1\ShengfutongController;
use App\Http\Controllers\Admin\Api\V1\ScheduledTaskController;
use App\Http\Controllers\Admin\Api\V1\InstitutionSummaryController;
use App\Http\Controllers\Admin\Api\V1\BalanceManagementController;
use App\Http\Controllers\Admin\Api\V1\WithdrawalAuditController;
use App\Http\Controllers\Admin\Api\V1\BranchOrganizationController;
use App\Http\Controllers\WechatMenuController;
use App\Http\Controllers\Platform\FansController;
use App\Http\Controllers\Platform\FanGroupController;
use App\Http\Controllers\Platform\FanTagController;


/*
|--------------------------------------------------------------------------
| 地址API路由 - 完全绕过所有中间件（最高优先级）
|--------------------------------------------------------------------------
*/

// 地址API路由 - 腾讯位置服务代理（完全公开访问，绕过所有中间件）
Route::group(['prefix' => 'address'], function () {
    Route::get('/provinces', [\App\Http\Controllers\Admin\Api\V1\AddressController::class, 'getProvinces']);
    Route::get('/cities', [\App\Http\Controllers\Admin\Api\V1\AddressController::class, 'getCities']);
    Route::get('/districts', [\App\Http\Controllers\Admin\Api\V1\AddressController::class, 'getDistricts']);
    Route::post('/geocode', [\App\Http\Controllers\Admin\Api\V1\AddressController::class, 'geocode']);
});

// 测试分类管理API - 公开访问（用于测试）
Route::group(['prefix' => 'test-mall'], function () {
    Route::get('/categories', [\App\Http\Controllers\Admin\Web\MallCategoryController::class, 'index']);
    Route::get('/categories/stats', [\App\Http\Controllers\Admin\Web\MallCategoryController::class, 'getStats']);
    Route::post('/categories', [\App\Http\Controllers\Admin\Web\MallCategoryController::class, 'store']);
    Route::put('/categories/{id}', [\App\Http\Controllers\Admin\Web\MallCategoryController::class, 'update']);
    Route::delete('/categories/{id}', [\App\Http\Controllers\Admin\Web\MallCategoryController::class, 'destroy']);
    Route::put('/categories/{id}/status', [\App\Http\Controllers\Admin\Web\MallCategoryController::class, 'updateStatus']);
});

/*
|--------------------------------------------------------------------------
| 完全公开的VIP API路由 - 绕过所有中间件
|--------------------------------------------------------------------------
*/

// 手机端VIP API - 完全绕过所有中间件，包括api中间件组
Route::group(['prefix' => 'mobile/v1/vip'], function () {
    // 获取VIP团队信息
    Route::match(['get', 'post'], 'team-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getTeamInfo']);
    
    // 获取VIP奖金池信息
    Route::match(['get', 'post'], 'pool-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getPoolInfo']);
    
    // 获取VIP分红信息
    Route::match(['get', 'post'], 'dividend-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getDividendInfo']);
});

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned the "api" middleware group. Make something great!
|
*/

// 简单测试路由
Route::get('/test-water-points', function () {
    return response()->json([
        'code' => 0,
        'message' => 'Test successful',
        'data' => ['test' => true]
    ]);
});

// 导航管理API - 不需要认证，独立于认证组
Route::prefix('admin/v1/navs')->group(function () {
    // 首页导航
    Route::prefix('home')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\HomeNavController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\HomeNavController::class, 'store']);
        Route::get('/{id}', [App\Http\Controllers\Api\HomeNavController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\Api\HomeNavController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Api\HomeNavController::class, 'destroy']);
    });

    // 底部导航
    Route::prefix('tabbar')->group(function () {
        Route::get('/', [App\Http\Controllers\Api\TabbarNavController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Api\TabbarNavController::class, 'store']);
        Route::get('/{id}', [App\Http\Controllers\Api\TabbarNavController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\Api\TabbarNavController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Api\TabbarNavController::class, 'destroy']);
    });
});

// 管理后台API路由
Route::prefix('admin/v1')->middleware(['auth:sanctum'])->group(function () {
    
    // 取水点管理
    Route::apiResource('water-points', \App\Http\Controllers\Admin\Api\V1\WaterPointController::class);
    Route::put('water-points/{id}/status', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'updateStatus']);
    Route::put('water-points/{id}/open-status', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'updateOpenStatus']);
    Route::get('water-points/statistics', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'statistics']);
    Route::get('water-points/nearby', [\App\Http\Controllers\Admin\Api\V1\WaterPointController::class, 'nearby']);
    
    // 文件上传
    Route::post('upload', [\App\Http\Controllers\Admin\Api\V1\UploadController::class, 'upload']);
    Route::get('upload/config', [\App\Http\Controllers\Admin\Api\V1\UploadController::class, 'config']);
    Route::delete('upload', [\App\Http\Controllers\Admin\Api\V1\UploadController::class, 'delete']);
    
});



/*
|--------------------------------------------------------------------------
| 分支机构VIP API路由 - 完全绕过所有中间件（最高优先级）
|--------------------------------------------------------------------------
*/

// 分支机构VIP相关路由 - 完全公开访问，绕过所有中间件
Route::group(['prefix' => 'mobile/v1/branch-vip'], function () {
    Route::match(['get', 'post'], 'dividend-stats', [\App\Http\Controllers\Mobile\Api\V1\BranchVipController::class, 'getDividendStats']);
    Route::match(['get', 'post'], 'team-data', [\App\Http\Controllers\Mobile\Api\V1\BranchVipController::class, 'getTeamData']);
    Route::match(['get', 'post'], 'time-info', [\App\Http\Controllers\Mobile\Api\V1\BranchVipController::class, 'getTimeInfo']);
    Route::match(['get', 'post'], 'dividend-detail', [\App\Http\Controllers\Mobile\Api\V1\BranchVipController::class, 'getDividendDetail']);
});

// 分支机构业务员相关路由 - 完全公开访问，绕过所有中间件
Route::group(['prefix' => 'branch/salesman'], function () {
    Route::get('info', [\App\Http\Controllers\Api\BranchSalesmanController::class, 'info']);
    Route::get('stats', [\App\Http\Controllers\Api\BranchSalesmanController::class, 'stats']);
    Route::get('customers', [\App\Http\Controllers\Api\BranchSalesmanController::class, 'customers']);
    Route::get('ranking', [\App\Http\Controllers\Api\BranchSalesmanController::class, 'ranking']);
})->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf']);

// 分支机构用户相关路由 - 需要分支机构token验证
Route::group(['prefix' => 'branch/user'], function () {
    Route::get('info', [\App\Http\Controllers\Api\BranchUserController::class, 'info']);
    Route::get('profile', [\App\Http\Controllers\Api\BranchUserController::class, 'profile']);
    Route::get('settings', [\App\Http\Controllers\Api\BranchUserController::class, 'settings']);
    Route::post('update', [\App\Http\Controllers\Api\BranchUserController::class, 'update']);
});

// 分支机构VIP相关路由 - 需要分支机构token验证
Route::group(['prefix' => 'branch/vip'], function () {
    Route::get('info', [\App\Http\Controllers\Api\BranchVipController::class, 'info']);
    Route::get('stats', [\App\Http\Controllers\Api\BranchVipController::class, 'stats']);
    Route::get('team', [\App\Http\Controllers\Api\BranchVipController::class, 'team']);
    Route::get('dividend', [\App\Http\Controllers\Api\BranchVipController::class, 'dividend']);
});

// 手机端公开API - V1版本（不需要认证）
Route::prefix('mobile/v1')->group(function () {
    // VIP相关路由
    Route::prefix('vip')->group(function () {
        // 获取VIP团队信息 - 支持GET和POST方法
        Route::match(['get', 'post'], 'team-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getTeamInfo']);
        
        // 获取VIP奖金池信息 - 支持GET和POST方法
        Route::match(['get', 'post'], 'pool-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getPoolInfo']);
        
        // 获取VIP分红信息 - 支持GET和POST方法
        Route::match(['get', 'post'], 'dividend-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getDividendInfo']);
    });

    // 取水点相关API
    Route::prefix('water-points')->group(function () {
        // 获取取水点列表
        Route::get('/', [WaterPointController::class, 'index']);
        
        // 获取取水点详情
        Route::get('/{id}', [WaterPointController::class, 'show']);
        
        // 获取附近取水点
        Route::get('/nearby/search', [WaterPointController::class, 'nearby']);
        
        // 收藏/取消收藏取水点（需要认证）
        Route::post('/favorite/toggle', [WaterPointController::class, 'toggleFavorite'])->middleware('auth:sanctum');
    });
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| 公共API接口
|--------------------------------------------------------------------------
*/

// 短信验证码相关API路由 - 任何域名都可访问
Route::prefix('sms')->group(function () {
    Route::post('/send', [SmsController::class, 'send']);
    Route::post('/verify', [SmsController::class, 'verify']);
});

// 分支机构管理员登录API - 公开访问
Route::post('/branch/admin-login', [\App\Http\Controllers\Admin\Api\V1\BranchOrganizationController::class, 'adminLogin']);

// 根据代码获取分支机构信息API - 公开访问（用于登录页面）
Route::get('/branch/info', [\App\Http\Controllers\Admin\Api\V1\BranchOrganizationController::class, 'getByCode']);

// 根据ID获取分支机构信息API - 公开访问（用于分支机构管理后台）
Route::get('/branch/{id}/info', [\App\Http\Controllers\Admin\Api\V1\BranchOrganizationController::class, 'show']);

// 分支机构微信菜单管理API - 使用Platform控制器
Route::prefix('admin/v1/branches/{branchId}/wechat/menu')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 菜单显示状态管理
    Route::get('/display-status', [\App\Http\Controllers\Platform\MenuController::class, 'getDisplayStatus']);
    Route::post('/display-status', [\App\Http\Controllers\Platform\MenuController::class, 'setDisplayStatus']);
    
    // 菜单CRUD操作
    Route::get('/', [\App\Http\Controllers\Platform\MenuController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Platform\MenuController::class, 'store']);
    Route::get('/{id}', [\App\Http\Controllers\Platform\MenuController::class, 'show']);
    Route::put('/{id}', [\App\Http\Controllers\Platform\MenuController::class, 'update']);
    Route::delete('/{id}', [\App\Http\Controllers\Platform\MenuController::class, 'destroy']);
    
    // 菜单操作
    Route::post('/{id}/copy', [\App\Http\Controllers\Platform\MenuController::class, 'copy']);
    Route::post('/{id}/publish', [\App\Http\Controllers\Platform\MenuController::class, 'publish']);
    Route::post('/sync', [\App\Http\Controllers\Platform\MenuController::class, 'sync']);
    Route::get('/current', [\App\Http\Controllers\Platform\MenuController::class, 'getCurrentMenu']);
    
    // 素材管理
    Route::get('/materials', [\App\Http\Controllers\Platform\MenuController::class, 'getMaterials']);
    Route::get('/materials/{id}', [\App\Http\Controllers\Platform\MenuController::class, 'getMaterialDetail']);
    
    // 关键字管理
    Route::get('/keywords', [\App\Http\Controllers\Platform\MenuController::class, 'getKeywords']);
    Route::get('/keywords/{id}', [\App\Http\Controllers\Platform\MenuController::class, 'getKeywordDetail']);
    
    // 粉丝分组管理
    Route::get('/fan-groups', [\App\Http\Controllers\Platform\MenuController::class, 'getFanGroups']);
});

// 分支机构微信自动回复管理API - 使用Platform控制器
Route::prefix('admin/v1/branches/{branchId}/wechat/auto-reply')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 自动回复规则CRUD操作
    Route::get('/', [\App\Http\Controllers\Platform\AutoReplyController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Platform\AutoReplyController::class, 'store']);
    Route::get('/{ruleId}', [\App\Http\Controllers\Platform\AutoReplyController::class, 'show']);
    Route::delete('/{ruleId}', [\App\Http\Controllers\Platform\AutoReplyController::class, 'destroy']);
    
    // 批量操作
    Route::post('/batch-delete', [\App\Http\Controllers\Platform\AutoReplyController::class, 'batchDestroy']);
    
    // 状态管理
    Route::patch('/{ruleId}/status', [\App\Http\Controllers\Platform\AutoReplyController::class, 'updateStatus']);
    
    // 关键字检查
    Route::post('/check-keyword', [\App\Http\Controllers\Platform\AutoReplyController::class, 'checkKeyword']);
    
    // 特殊消息回复设置
    Route::get('/special', [\App\Http\Controllers\Platform\AutoReplyController::class, 'getSpecialReplies']);
    Route::post('/special', [\App\Http\Controllers\Platform\AutoReplyController::class, 'updateSpecialReply']);
});

// 分支机构微信菜单管理API - 兼容旧版本
Route::prefix('branches/{branch_id}/wechat/menu')->group(function () {
    Route::get('/', [WechatMenuController::class, 'index']);
    Route::post('/', [WechatMenuController::class, 'store']);
    Route::post('/sync', [WechatMenuController::class, 'sync']);
    Route::delete('/', [WechatMenuController::class, 'delete']);
    Route::get('/templates', [WechatMenuController::class, 'templates']);
    Route::post('/apply-template', [WechatMenuController::class, 'applyTemplate']);
});

// 微信JSSDK配置API - 显式定义为公开访问
Route::get('/wechat/jsconfig', [\App\Http\Controllers\Api\WechatJssdkController::class, 'getConfig'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web']);

// 标准RESTful API路径 - 显式定义为公开访问
Route::get('/api/wechat/jsconfig', [\App\Http\Controllers\Api\WechatJssdkController::class, 'getConfig'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle']);

// 分支机构微信JSSDK配置API - 显式定义为公开访问
Route::get('/branch/wechat/jsconfig', [\App\Http\Controllers\Api\BranchWechatJssdkController::class, 'getConfig'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle']);

Route::get('/api/branch/wechat/jsconfig', [\App\Http\Controllers\Api\BranchWechatJssdkController::class, 'getConfig'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle']);

// 微信登录URL API已在下方统一定义

/*
|--------------------------------------------------------------------------
| 微信登录回调处理API - 统一RESTful风格
|--------------------------------------------------------------------------
|
| 以下路由处理微信登录回调，支持多种路径格式和请求方法
| 所有路由都指向同一个控制器方法，确保一致性
| 所有路由都显式定义为公开访问，不需要任何中间件
|
*/

// 标准RESTful API路径 - 主要路径
Route::match(['get', 'post', 'options'], '/api/auth/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.auth.wechat.callback');

// 简化的RESTful API路径 - 已删除重复路由

// 不带api前缀的RESTful路径
Route::match(['get', 'post', 'options'], '/auth/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('auth.wechat.callback');

Route::match(['get', 'post', 'options'], '/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('wechat.callback');

// 兼容旧版API路径 - 显式定义为公开访问
Route::match(['get', 'post', 'options'], '/wechat/login_callback', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('wechat.login_callback');

Route::match(['get', 'post', 'options'], '/api/wechat/login_callback', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.wechat.login_callback');

Route::match(['get', 'post', 'options'], '/wechat_login_callback', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('wechat_login_callback');

Route::match(['get', 'post', 'options'], '/api/wechat_login_callback', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.wechat_login_callback');

// 直接callback路径
Route::match(['get', 'post', 'options'], '/callback', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('callback');

// 添加autologin测试路径
Route::match(['get', 'post', 'options'], '/api/auth/wechat/autologin', [\App\Http\Controllers\Api\WechatController::class, "handleCallback"])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.auth.wechat.autologin');

// 微信登录URL获取RESTful API
Route::get('/api/wechat/login-url', [\App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.wechat.login-url');

Route::get('/api/wechat/auth-url', [\App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.wechat.auth-url');

Route::get('/wechat/login-url', [\App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('wechat.login-url');

// 新增微信用户角色检查API
Route::post('/api/wechat/check-user-roles', [\App\Http\Controllers\Api\WechatController::class, 'checkUserRoles'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.wechat.check-user-roles');

// 微信绑定手机号API
Route::post('/api/wechat/bind-phone', [\App\Http\Controllers\Api\WechatController::class, 'bindPhone'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.wechat.bind-phone');

// 分支机构微信登录API
Route::prefix('branch-wechat')->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])->group(function () {
    // 获取分支机构微信登录URL
    Route::get('/login-url', [\App\Http\Controllers\Api\BranchWechatAuthController::class, 'getLoginUrl'])
        ->name('branch-wechat.login-url');
    
    // 处理分支机构微信登录回调
    Route::match(['get', 'post', 'options'], '/callback', [\App\Http\Controllers\Api\BranchWechatAuthController::class, 'handleCallback'])
        ->name('branch-wechat.callback');
    
    // 验证分支机构用户token
    Route::post('/verify-token', [\App\Http\Controllers\Api\BranchWechatAuthController::class, 'verifyToken'])
        ->name('branch-wechat.verify-token');
});

/*
|--------------------------------------------------------------------------
| 通知相关API路由
|--------------------------------------------------------------------------
*/

// 商户通知API路由
Route::prefix('merchant')->middleware('auth:sanctum')->group(function () {
    // 获取通知列表
    Route::get('/notifications', [\App\Http\Controllers\Api\V1\NotificationController::class, 'index']);
    
    // 获取未读通知数量
    Route::get('/notifications/unread-count', [\App\Http\Controllers\Api\V1\NotificationController::class, 'unreadCount']);
    
    // 标记通知为已读
    Route::post('/notifications/{id}/read', [\App\Http\Controllers\Api\V1\NotificationController::class, 'markAsRead']);
    
    // 标记所有通知为已读
    Route::post('/notifications/read-all', [\App\Http\Controllers\Api\V1\NotificationController::class, 'markAllAsRead']);
    
    // 清空所有通知
    Route::delete('/notifications/clear', [\App\Http\Controllers\Api\V1\NotificationController::class, 'clearAll']);
    
    // 获取通知设置
    Route::get('/notification-settings', [\App\Http\Controllers\Api\V1\NotificationController::class, 'getSettings']);
    
    // 更新通知设置
    Route::post('/notification-settings', [\App\Http\Controllers\Api\V1\NotificationController::class, 'updateSettings']);
});

// 兼容简短路径
Route::get('/wechat/auth-url', [\App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('wechat.auth-url');

// APP端用户认证相关API路由
Route::prefix('auth')->group(function () {
    // 无需认证的路由 - 确保这些路由是公开的，不需要任何中间件
    Route::withoutMiddleware(['auth:sanctum'])->group(function () {
        Route::get('/wechat/config', [AuthController::class, 'getWechatConfig']);
        // 微信登录URL和回调已在其他地方统一定义
        Route::post('/bind-phone', [AuthController::class, 'bindPhone']);
        Route::post('/auto-bind-phone', [AuthController::class, 'autoBindPhone']);
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/login/code', [AuthController::class, 'loginByVerificationCode']);
        Route::post('/register', [AuthController::class, 'register']);
        Route::post('/wechat-login', [AuthController::class, 'wechatLogin']);
        Route::post('/verify-token', [AuthController::class, 'verifyToken']);
    });

    // 需要认证的路由
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/user', [AuthController::class, 'getUser']);
        Route::put('/user', [AuthController::class, 'updateUser']);
        
        // Token管理路由
        Route::post('/token/refresh', [\App\Http\Controllers\Api\TokenController::class, 'refresh']);
        Route::get('/token/status', [\App\Http\Controllers\Api\TokenController::class, 'status']);
    });
});

// 用户角色同步API
Route::prefix('users')->group(function () {
    Route::post('sync-roles', [AuthController::class, 'syncUserRoles']);
});

// 系统设置获取API - 公开接口，不需要认证
Route::prefix('settings')->withoutMiddleware(['auth:sanctum'])->group(function () {
    // 微信配置
    Route::get('/wechat', [SettingsController::class, 'getWechatConfig']);
    Route::post('/wechat', [SettingsController::class, 'saveWechatConfig'])->middleware('auth:sanctum');

    // 短信模块配置路由 - 修复：传递正确的模块参数
    Route::get('/sms', function(Request $request) {
        return app(SettingsController::class)->getModuleConfigs($request, 'sms');
    });
    Route::post('/sms', function(Request $request) {
        return app(SettingsController::class)->saveModuleConfig($request, 'sms');
    })->middleware('auth:sanctum');

    // 添加测试发送短信路由
    Route::post('/test-sms', [SmsController::class, 'testSend'])->middleware('auth:sanctum');

    // 通用模块配置路由
    Route::get('/{module}', [SettingsController::class, 'getModuleConfigs']);
    Route::post('/{module}', [SettingsController::class, 'saveModuleConfig'])->middleware('auth:sanctum');
});

// 导航配置API
Route::prefix('nav')->group(function () {
    Route::get('/config', [NavController::class, 'getConfig']);
    Route::post('/config', [NavController::class, 'saveConfig'])->middleware('auth:sanctum');
});

// APP前端接口 - 已废弃
// Route::prefix('index.php')->group(function () {
//     Route::get('banners', [IndexController::class, 'getBanners']);
//     Route::get('mall_info', [IndexController::class, 'getMallInfo']);
//     Route::get('nav_items', [IndexController::class, 'getNavItems']);
// });

// APP前端接口
Route::prefix('app')->group(function () {
    // 公共接口 - 不需要认证
    Route::prefix('public')->withoutMiddleware(['api'])->group(function () {
        // 首页导航
        Route::get('/home-nav', [App\Http\Controllers\Api\App\HomeNavController::class, 'getActiveNavItems']);
        // 底部导航
        Route::get('/tabbar', [App\Http\Controllers\Api\App\TabbarNavController::class, 'getActiveNavItems']);
        // Banner
        Route::get('/banners', [App\Http\Controllers\Api\App\BannerController::class, 'index']);
        Route::get('/banners/{id}', [App\Http\Controllers\Api\App\BannerController::class, 'show']);
        // 商品分类
        Route::get('/categories', [App\Http\Controllers\Api\App\CategoryController::class, 'index']);
        Route::get('/categories/{id}', [App\Http\Controllers\Api\App\CategoryController::class, 'show']);
        // 商品
        Route::get('/products', [App\Http\Controllers\Api\App\ProductController::class, 'index']);
        Route::get('/products/featured', [App\Http\Controllers\Api\App\ProductController::class, 'featured']);
        Route::get('/products/{id}', [App\Http\Controllers\Api\App\ProductController::class, 'show']);

        // 支付回调
        Route::post('/payment/notify/wechat', [App\Http\Controllers\Api\OrderController::class, 'paymentNotify']);
        Route::post('/payment/notify/alipay', [App\Http\Controllers\Api\OrderController::class, 'paymentNotify']);
    });

    // 添加微信登录回调API
    Route::match(['get', 'post', 'options'], '/wechat_login_callback', [\App\Http\Controllers\Api\WechatAuthController::class, "handleCallback"])
        ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
        ->name('api.app.wechat_login_callback');

    // 微信登录相关接口 - 不需要认证的接口
    Route::withoutMiddleware(['auth:api', 'csrf'])->group(function () {
        // 获取微信登录URL
        Route::get('/wechat_login_url', [App\Http\Controllers\Api\WechatAuthController::class, 'getLoginUrl']);
        // 微信登录回调
        Route::match(['get', 'post', 'options'], '/wechat_login_callback', [App\Http\Controllers\Api\WechatAuthController::class, "handleCallback"]);
    });

    // 需要认证的接口
    Route::middleware('auth:sanctum')->group(function () {
        // 订单相关接口
        Route::prefix('orders')->group(function () {
            Route::post('/create', [App\Http\Controllers\Api\OrderController::class, 'create']);
            Route::get('/status', [App\Http\Controllers\Api\OrderController::class, 'status']);
            Route::get('/user', [App\Http\Controllers\Api\OrderController::class, 'getUserOrders']);
            Route::post('/cancel/{orderNo}', [App\Http\Controllers\Api\OrderController::class, 'cancel']);
        });
    });
});

// 示例API (已禁用)
// Route::get('/example', [App\Http\Controllers\Api\ExampleController::class, 'index']);

// 创建默认配置的公开API
Route::get('/create-default-configs', function () {
    $wechatConfigs = [
        // 微信小程序相关配置
        [
            'module' => 'wechat',
            'key' => 'app_id',
            'value' => env('WECHAT_MINI_PROGRAM_APPID', ''),
            'type' => 'string',
            'description' => '微信小程序AppID',
            'is_system' => 1,
            'is_frontend' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'module' => 'wechat',
            'key' => 'app_secret',
            'value' => env('WECHAT_MINI_PROGRAM_SECRET', ''),
            'type' => 'string',
            'description' => '微信小程序AppSecret',
            'is_system' => 1,
            'is_frontend' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ],

        // 微信公众号相关配置
        [
            'module' => 'wechat',
            'key' => 'official_app_id',
            'value' => env('WECHAT_OFFICIAL_ACCOUNT_APPID', ''),
            'type' => 'string',
            'description' => '微信公众号AppID',
            'is_system' => 1,
            'is_frontend' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'module' => 'wechat',
            'key' => 'official_app_secret',
            'value' => env('WECHAT_OFFICIAL_ACCOUNT_SECRET', ''),
            'type' => 'string',
            'description' => '微信公众号AppSecret',
            'is_system' => 1,
            'is_frontend' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ],

        // 微信开放平台相关配置
        [
            'module' => 'wechat',
            'key' => 'open_app_id',
            'value' => env('WECHAT_OPEN_PLATFORM_APPID', ''),
            'type' => 'string',
            'description' => '微信开放平台AppID',
            'is_system' => 1,
            'is_frontend' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'module' => 'wechat',
            'key' => 'open_app_secret',
            'value' => env('WECHAT_OPEN_PLATFORM_SECRET', ''),
            'type' => 'string',
            'description' => '微信开放平台AppSecret',
            'is_system' => 1,
            'is_frontend' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ],
    ];

    foreach ($wechatConfigs as $config) {
        \DB::table('system_configs')->updateOrInsert(
            ['module' => $config['module'], 'key' => $config['key']],
            $config
        );
    }

    return response()->json([
        'code' => 0,
        'message' => '默认配置已创建',
        'data' => null
    ]);
});

/*
|--------------------------------------------------------------------------
| 需要身份验证的API接口
|--------------------------------------------------------------------------
*/

// 积分系统API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('points')->group(function () {
    // 用户积分信息
    Route::get('/user', [\App\Http\Controllers\Api\PointController::class, 'getUserPoints']);

    // 用户积分记录
    Route::get('/user-records', [\App\Http\Controllers\Api\PointController::class, 'getUserPointRecords']);

    // 用户资产信息 (新增)
    Route::get('/user-assets', [\App\Http\Controllers\Api\PointController::class, 'getUserAssets']);

    // 用户订单统计 (新增)
    Route::get('/user-order-stats', [\App\Http\Controllers\Api\PointController::class, 'getUserOrderStats']);

    // 积分规则
    Route::get('/app-rules', [\App\Http\Controllers\Api\PointController::class, 'getPointRules']);

    // 积分兑换相关
    Route::get('/exchange-items', [\App\Http\Controllers\Api\PointController::class, 'getExchangeItems']);
    Route::post('/exchange', [\App\Http\Controllers\Api\PointController::class, 'exchange']);
    Route::get('/exchange-records', [\App\Http\Controllers\Api\PointController::class, 'getExchangeRecords']);
});

// App用户管理相关API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('app-users')->group(function () {
    // 用户设备管理API
    Route::prefix('/{userId}/devices')->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'store']);
    });
});

// 系统管理相关API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('system')->group(function () {
    // 系统管理相关API路由已移至Admin命名空间
});

/*
|--------------------------------------------------------------------------
| 管理后台API路由
|--------------------------------------------------------------------------
*/

Route::prefix('admin')->group(function () {
    // 管理员登录路由
    Route::post('/login', [AdminAuthController::class, 'login']);

    // 令牌验证和刷新 - 公开接口
    Route::post('/verify-token', [AdminAuthController::class, 'verifyToken']);
    Route::post('/refresh-token', [AdminAuthController::class, 'refreshToken']);

    // APP用户管理API路由 - 需要认证
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/app-users', [AdminApiAppUserController::class, 'index']);
        Route::post('/app-users', [AdminApiAppUserController::class, 'store']);
        Route::get('/app-users/{id}', [AdminApiAppUserController::class, 'show']);
        Route::put('/app-users/{id}', [AdminApiAppUserController::class, 'update']);
        Route::delete('/app-users/{id}', [AdminApiAppUserController::class, 'destroy']);
        Route::post('/app-users/sync-to-salesmen', [AdminApiAppUserController::class, 'syncToSalesmen']);
        Route::get('/app-users/{id}/sync-roles', [AdminApiAppUserController::class, 'syncUserRoles']);
        Route::post('/app-users/sync-roles', [AdminApiAppUserController::class, 'syncAllUsersRoles']);
        Route::get('/app-users/sync-progress', [AdminApiAppUserController::class, 'getSyncProgress']);
    });

    // 菜单API - RESTful风格 - 公开接口
    Route::prefix('menus')->withoutMiddleware(['auth:sanctum'])->group(function () {
        // 获取所有菜单（用于前端显示）
        Route::get('/', [\App\Http\Controllers\Admin\AdminMenuController::class, 'getMenus'])->name('api.admin.menus.index');

        // 兼容旧版前端的路径
        Route::get('/list', [\App\Http\Controllers\Admin\AdminMenuController::class, 'getMenus'])->name('api.admin.menus.list');
    });

    // 菜单获取 - 额外路径，确保兼容性
    Route::get('/api/menus', [\App\Http\Controllers\Admin\AdminMenuController::class, 'getMenus'])->name('api.admin.api.menus.get')->withoutMiddleware(['auth:sanctum']);

    // 原始菜单路径 - 确保兼容性
    Route::get('/menus', [\App\Http\Controllers\Admin\AdminMenuController::class, 'getMenus'])->name('api.menus.get')->withoutMiddleware(['auth:sanctum']);

    // 系统设置 - 公开获取
    Route::get('/settings', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'getSettings']);
    Route::get('/site-settings', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'getSiteSettings']);

    // Banner管理 - 公开获取
    Route::get('/banners', [\App\Http\Controllers\Api\App\BannerController::class, 'index'])->withoutMiddleware(['auth:sanctum']);

    // 设备管理API路由 - 公开访问
    Route::prefix('devices')->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\DeviceController::class, 'index']);
        Route::get('/clients', [\App\Http\Controllers\Api\DeviceController::class, 'clients']);
    });

    // Tapp设备管理API路由 - 公开访问
    Route::prefix('tapp-devices')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'index']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'show'])->where('id', '[0-9]+');
        Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'getAppUsers']);
        Route::post('/sync', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'syncData']);

        // 需要认证的路由
        Route::middleware('auth:sanctum')->group(function () {
            Route::put('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'update'])->where('id', '[0-9]+');
            Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'updateStatus'])->where('id', '[0-9]+');
            Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'destroy'])->where('id', '[0-9]+');
        });
    });



    // 需要认证的后台管理路由
    Route::middleware('auth:admin')->group(function () {
        // 用户信息和认证
        Route::get('/me', [AdminAuthController::class, 'me']);
        Route::post('/logout', [AdminAuthController::class, 'logout']);

        // 用户个人资料管理
        Route::post('/upload-avatar', [UserProfileController::class, 'uploadAvatar']);
        Route::post('/update-profile', [UserProfileController::class, 'updateProfile']);
        Route::post('/change-password', [UserProfileController::class, 'changePassword']);

        // 微信绑定相关
        Route::post('/wechat/binding', [UserProfileController::class, 'updateWechatBinding']);
        Route::get('/wechat/qrcode', [UserProfileController::class, 'getWechatQrCode']);
        Route::get('/wechat/binding/status', [UserProfileController::class, 'checkWechatBindStatus']);

        // 菜单管理 - RESTful风格
        Route::apiResource('menus', \App\Http\Controllers\Admin\AdminMenuController::class);

        // 兼容旧版路径
        Route::prefix('menu-management')->group(function () {
            Route::get('/', [AdminMenuController::class, 'index']);
            Route::post('/', [AdminMenuController::class, 'store']);
            Route::get('/{id}', [AdminMenuController::class, 'show']);
            Route::put('/{id}', [AdminMenuController::class, 'update']);
            Route::delete('/{id}', [AdminMenuController::class, 'destroy']);
        });

        // 测试路由API
        Route::get('/test-routes', [AdminMenuController::class, 'testRoutes']);

        // 后台管理员管理
        Route::prefix('admins')->group(function () {
            Route::get('/', [AdminUserController::class, 'index']);
            Route::post('/', [AdminUserController::class, 'store']);
            Route::get('/{id}', [AdminUserController::class, 'show']);
            Route::put('/{id}', [AdminUserController::class, 'update']);
            Route::delete('/{id}', [AdminUserController::class, 'destroy']);
        });

        // 角色管理
        Route::prefix('roles')->group(function () {
            Route::get('/', [RoleController::class, 'index']);
            Route::post('/', [RoleController::class, 'store']);
            Route::get('/permissions/all', [RoleController::class, 'getAllPermissions']);
            Route::get('/{id}', [RoleController::class, 'show']);
            Route::put('/{id}', [RoleController::class, 'update']);
            Route::delete('/{id}', [RoleController::class, 'destroy']);
            Route::post('/{id}/permissions', [RoleController::class, 'updatePermissions']);
        });

        // 权限管理
        Route::prefix('permissions')->group(function () {
            Route::get('/', [PermissionController::class, 'index']);
            Route::post('/', [PermissionController::class, 'store']);
            Route::get('/modules', [PermissionController::class, 'getModules']);
            Route::get('/{id}', [PermissionController::class, 'show']);
            Route::put('/{id}', [PermissionController::class, 'update']);
            Route::delete('/{id}', [PermissionController::class, 'destroy']);
        });

        // 通知管理路由已移至auth:sanctum中间件组

        // APP用户管理
        Route::prefix('app-users')->group(function () {
            Route::get('/', [AdminAppUserController::class, 'index']);
            Route::get('/{id}', [AdminAppUserController::class, 'show']);
            Route::put('/{id}', [AdminAppUserController::class, 'update']);
            Route::delete('/{id}', [AdminAppUserController::class, 'destroy']);
            Route::post('/sync-roles', [AdminAppUserController::class, 'syncRoles']);
            Route::get('/sync-progress', [AdminAppUserController::class, 'getSyncProgress']);
            Route::post('/sync-to-salesmen', [AdminAppUserController::class, 'syncToSalesmen']);

            // 单个用户角色同步
            Route::get('/{id}/sync-roles', [AdminAppUserController::class, 'syncUserRoles']);

            // 用户设备管理API
            Route::prefix('/{userId}/devices')->group(function () {
                Route::get('/', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'index']);
                Route::post('/', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'store']);
            });
        });

        // 系统设置更新
        Route::post('/settings/update', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'updateSettings']);
        Route::post('/settings/upload-logo', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'uploadLogo']);
        Route::post('/settings/upload-favicon', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'uploadFavicon']);

        // 商城管理 - 轮播图管理
        Route::prefix('banners')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'index']);
            Route::post('/', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'store']);
            Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'show']);
            Route::post('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'update']);
            Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'destroy']);
            Route::post('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'updateStatus']);
        });

        // 合作伙伴管理
        Route::prefix('partners')->group(function () {
            Route::get('/', [PartnerController::class, 'index']);
            Route::get('/{id}', [PartnerController::class, 'show']);
            Route::put('/{id}', [PartnerController::class, 'update']);
            Route::patch('/{id}/status', [PartnerController::class, 'changeStatus']);
            Route::post('/{id}/balance', [PartnerController::class, 'adjustBalance']);
            Route::post('/{id}/points', [PartnerController::class, 'adjustPoints']);
        });

        // 商城管理 - 商品分类
        Route::prefix('categories')->group(function () {
            Route::get('/', [ProductCategoryController::class, 'index']);
            Route::post('/', [ProductCategoryController::class, 'store']);
            Route::get('/all', [ProductCategoryController::class, 'all']);
            Route::get('/{id}', [ProductCategoryController::class, 'show']);
            Route::put('/{id}', [ProductCategoryController::class, 'update']);
            Route::delete('/{id}', [ProductCategoryController::class, 'destroy']);
        });

        // 商城管理 - 订单管理
        Route::prefix('orders')->group(function () {
            Route::get('/', [OrderController::class, 'index']);
            Route::get('/statistics', [OrderController::class, 'statistics']);
            Route::get('/export', [OrderController::class, 'export']);
            Route::get('/{id}', [OrderController::class, 'show']);
            Route::post('/{id}/ship', [OrderController::class, 'ship']);
            Route::post('/{id}/confirm', [OrderController::class, 'confirm']);
            Route::post('/{id}/cancel', [OrderController::class, 'cancel']);
            Route::put('/{id}/address', [OrderController::class, 'updateAddress']);
            Route::post('/batch-ship', [OrderController::class, 'batchShip']);
        });

        // 商城管理 - 订单退款
        Route::prefix('order-refunds')->group(function () {
            Route::get('/', [OrderRefundController::class, 'index']);
            Route::get('/statistics', [OrderRefundController::class, 'statistics']);
            Route::get('/{id}', [OrderRefundController::class, 'show']);
            Route::post('/{id}/audit', [OrderRefundController::class, 'audit']);
            Route::post('/{id}/refund', [OrderRefundController::class, 'refund']);
        });

        // 商城管理 - 物流管理
        Route::prefix('logistics')->group(function () {
            Route::get('/companies', [LogisticsController::class, 'getCompanies']);
            Route::get('/track/{code}', [LogisticsController::class, 'track']);
            Route::post('/track/update', [LogisticsController::class, 'updateTracking']);
        });

        // 商城管理 - 通用设置
        Route::prefix('mall')->group(function () {
            Route::get('/settings', [MallController::class, 'getSettings']);
            Route::post('/settings', [MallController::class, 'updateSettings']);
            Route::get('/statistics', [MallController::class, 'getStatistics']);
        });

        // 商城管理 - 商品管理
        Route::prefix('products')->group(function () {
            Route::get('/', [ProductController::class, 'index']);
            Route::get('/{id}', [ProductController::class, 'show']);
            Route::post('/', [ProductController::class, 'store']);
            Route::put('/{id}', [ProductController::class, 'update']);
            Route::delete('/{id}', [ProductController::class, 'destroy']);
        });

        // 安装预约管理 API
        // 安装预约API路由已移至Admin命名空间

        // 安装统计
        Route::get('installation/statistics', [\App\Http\Controllers\Api\InstallationStatisticsController::class, 'index']);
        Route::get('installation/statistics/export', [\App\Http\Controllers\Api\InstallationStatisticsController::class, 'export']);

        // 工程师管理
        Route::apiResource('installation/engineers', \App\Http\Controllers\Api\EngineerController::class);
        Route::get('installation/engineers/{id}/bookings', [\App\Http\Controllers\Api\EngineerController::class, 'getBookings']);
    });

    // 支付订单管理API
    Route::prefix('payment/orders')->group(function () {
        Route::get('/', [PaymentOrderController::class, 'index']);
        Route::get('/statistics', [PaymentOrderController::class, 'statistics']);
        Route::get('/export', [PaymentOrderController::class, 'export']);
        Route::get('/{id}', [PaymentOrderController::class, 'show']);
        Route::post('/{id}/ship', [PaymentOrderController::class, 'ship']);
        Route::post('/{id}/update-address', [PaymentOrderController::class, 'updateAddress']);
        Route::post('/{id}/cancel', [PaymentOrderController::class, 'cancel']);
        Route::post('/{id}/confirm', [PaymentOrderController::class, 'confirm']);
        Route::post('/batch-ship', [PaymentOrderController::class, 'batchShip']);
        Route::get('/export', [PaymentOrderController::class, 'export']);
        Route::get('/statistics', [PaymentOrderController::class, 'statistics']);
    });
});

// 支付方式管理API
Route::prefix('payment/methods')->group(function () {
    Route::get('/', [\App\Http\Controllers\Admin\Payment\PaymentMethodController::class, 'index']);
    Route::post('/update', [\App\Http\Controllers\Admin\Payment\PaymentMethodController::class, 'update']);
    Route::post('/{key}/toggle', [\App\Http\Controllers\Admin\Payment\PaymentMethodController::class, 'toggleStatus']);
});

// 支付交易记录API
Route::prefix('payment/transactions')->group(function () {
    Route::get('/', [\App\Http\Controllers\Admin\Payment\TransactionController::class, 'index']);
    Route::get('/statistics', [\App\Http\Controllers\Admin\Payment\TransactionController::class, 'statistics']);
    Route::get('/export', [\App\Http\Controllers\Admin\Payment\TransactionController::class, 'export']);
});

// 支付退款管理API
Route::prefix('payment/refunds')->group(function () {
    Route::get('/', [\App\Http\Controllers\Admin\Payment\RefundController::class, 'index']);
    Route::get('/statistics', [\App\Http\Controllers\Admin\Payment\RefundController::class, 'statistics']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Payment\RefundController::class, 'show']);
    Route::post('/{id}/audit', [\App\Http\Controllers\Admin\Payment\RefundController::class, 'audit']);
    Route::post('/{id}/refund', [\App\Http\Controllers\Admin\Payment\RefundController::class, 'refund']);
});

// 用户认证相关接口
Route::prefix('auth')->group(function () {
    Route::post('login', [App\Http\Controllers\Api\AuthController::class, 'login']);
    Route::post('register', [App\Http\Controllers\Api\AuthController::class, 'register']);
    Route::post('forgot-password', [App\Http\Controllers\Api\AuthController::class, 'forgotPassword']);

    // 微信登录相关接口已在其他地方统一定义

    // 需要认证的接口
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('logout', [App\Http\Controllers\Api\AuthController::class, 'logout']);
        Route::post('bind-phone', [App\Http\Controllers\Api\AuthController::class, 'bindPhone']);
    });
});

// 这些路由已在上面统一定义，此处删除重复定义

// 添加管理员登录路由
Route::post('admin/login', [App\Http\Controllers\Admin\Web\AdminAuthController::class, 'login']);

// 用户相关接口
Route::prefix('user')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('info', [App\Http\Controllers\Api\UserController::class, 'info']);
        Route::post('update-info', [App\Http\Controllers\Api\UserController::class, 'updateInfo']);
        Route::post('update-password', [App\Http\Controllers\Api\UserController::class, 'updatePassword']);
    });
});

// 添加一个新的路由，确保 /api/user/info 路由存在
Route::middleware('auth:sanctum')->get('/api/user/info', [App\Http\Controllers\Api\UserController::class, 'info']);

// 添加一个新的路由，确保 /user/info 路由存在
Route::middleware('auth:sanctum')->get('/user/info', [App\Http\Controllers\Api\UserController::class, 'info']);

// 兼容旧的API路径 - 用户信息相关
Route::prefix('api/user')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/info.php', [App\Http\Controllers\Api\UserController::class, 'info']);
        Route::post('/update_profile.php', [App\Http\Controllers\Api\UserController::class, 'updateInfo']);
        Route::post('/change_password.php', [App\Http\Controllers\Api\UserController::class, 'updatePassword']);

        // VIP相关API
        Route::get('/vip_workspace.php', [App\Http\Controllers\Api\Vip\WorkspaceController::class, 'index']);
        Route::get('/vip_pool_info.php', [App\Http\Controllers\Api\Vip\PoolInfoController::class, 'index']);
        Route::get('/vip_dividend_info.php', [App\Http\Controllers\Api\Vip\DividendInfoController::class, 'index']);
        Route::get('/vip_team_info.php', [App\Http\Controllers\Api\Vip\TeamInfoController::class, 'index']);
        Route::get('/vip_time_info.php', [App\Http\Controllers\Api\Vip\TimeInfoController::class, 'index']);
        Route::get('/vip_team_members.php', [App\Http\Controllers\Api\VipController::class, 'getTeamMembers']);
        Route::get('/vip_dividends.php', [App\Http\Controllers\Api\VipController::class, 'getDividends']);
        Route::get('/vip_reward_list.php', [App\Http\Controllers\Api\VipController::class, 'getRewardList']);
        Route::get('/vip_team.php', [App\Http\Controllers\Api\VipController::class, 'getTeam']);
    });
});

// 兼容旧的API路径 - 商城订单相关
Route::prefix('api/app/orders')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/create.php', [App\Http\Controllers\Api\OrderController::class, 'create']);
        Route::get('/status.php', [App\Http\Controllers\Api\OrderController::class, 'status']);
        Route::get('/user.php', [App\Http\Controllers\Api\OrderController::class, 'getUserOrders']);
        Route::post('/cancel.php', [App\Http\Controllers\Api\OrderController::class, 'cancel']);
        Route::get('/detail.php', [App\Http\Controllers\Api\OrderController::class, 'detail']);
    });
});

// 兼容旧的API路径 - Banner相关
Route::get('/banners.php', [App\Http\Controllers\Api\App\BannerController::class, 'index']);

// 兼容旧的API路径 - 底部导航相关
Route::get('/tabbar.php', [App\Http\Controllers\Api\TabbarController::class, 'index']);

// 兼容旧的API路径 - 首页导航相关
Route::get('/home.php', [App\Http\Controllers\Api\HomeNavController::class, 'index']);

// 兼容旧的API路径 - 商品分类相关
Route::get('/categories.php', [App\Http\Controllers\Api\App\CategoryController::class, 'index']);

// 兼容旧的API路径 - 商品相关
Route::get('/products.php', [App\Http\Controllers\Api\App\ProductController::class, 'index']);
Route::get('/product_detail.php', [App\Http\Controllers\Api\App\ProductController::class, 'show']);

// 兼容旧的API路径 - 取水点相关
Route::get('/water_points.php', [App\Http\Controllers\Api\WaterPointController::class, 'index']);
Route::get('/water_point_detail.php', [App\Http\Controllers\Api\WaterPointController::class, 'show']);
Route::get('/nearby_water_points.php', [App\Http\Controllers\Api\WaterPointController::class, 'nearby']);

// 兼容旧的API路径 - 用户相关
Route::prefix('user')->group(function () {
    // 不需要认证的接口
    Route::post('/login.php', [App\Http\Controllers\Api\UserLoginController::class, 'login']);
    Route::post('/login_by_sms.php', [App\Http\Controllers\Api\UserLoginController::class, 'loginBySms']);
    Route::post('/register.php', [App\Http\Controllers\Api\UserLoginController::class, 'register']);
    // VIP招募排行榜 - 不需要认证
    Route::get('/vip_recruit_ranking.php', [App\Http\Controllers\Api\VipController::class, 'getRecruitRanking']);

    // 用户信息API - 兼容旧版本，无需身份验证
    Route::get('/info.php', [App\Http\Controllers\Api\UserController::class, 'info']);
    Route::get('/assets.php', [App\Http\Controllers\Api\PointController::class, 'getUserAssets']);
    Route::get('/order_stats.php', [App\Http\Controllers\Api\PointController::class, 'getOrderStats']);

    // 需要认证的接口
    Route::middleware('auth:sanctum')->group(function () {
        // VIP相关API
        Route::get('/vip_workspace.php', [App\Http\Controllers\Api\Vip\WorkspaceController::class, 'index']);
        Route::get('/vip_pool_info.php', [App\Http\Controllers\Api\Vip\PoolInfoController::class, 'index']);
        Route::get('/vip_dividend_info.php', [App\Http\Controllers\Api\Vip\DividendInfoController::class, 'index']);
        Route::get('/vip_team_info.php', [App\Http\Controllers\Api\Vip\TeamInfoController::class, 'index']);
        Route::get('/vip_time_info.php', [App\Http\Controllers\Api\Vip\TimeInfoController::class, 'index']);
        Route::get('/vip_team_members.php', [App\Http\Controllers\Api\VipController::class, 'getTeamMembers']);
        Route::get('/vip_dividends.php', [App\Http\Controllers\Api\VipController::class, 'getDividends']);
        Route::get('/vip_reward_list.php', [App\Http\Controllers\Api\VipController::class, 'getRewardList']);
        Route::get('/vip_team.php', [App\Http\Controllers\Api\VipController::class, 'getTeam']);
    });
});

// 兼容旧的API路径 - 设备相关
Route::prefix('api/user')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/devices.php', [App\Http\Controllers\Api\DeviceController::class, 'userDevices']);
        Route::get('/purifier-device-detail.php', [App\Http\Controllers\Api\DeviceController::class, 'deviceDetail']);
        Route::get('/purifier-device-monitor.php', [App\Http\Controllers\Api\DeviceController::class, 'deviceStatus']);

        // 积分相关API
        Route::get('/assets.php', [App\Http\Controllers\Api\PointController::class, 'getUserAssets']);
        Route::get('/order_stats.php', [App\Http\Controllers\Api\PointController::class, 'getOrderStats']);

        // 商户相关API
        Route::get('/merchant_workspace.php', [App\Http\Controllers\Api\MerchantController::class, 'getWorkspace']);
        Route::get('/merchant_trade_list.php', [App\Http\Controllers\Api\MerchantController::class, 'getTradeList']);
        Route::get('/merchant_trade_detail.php', [App\Http\Controllers\Api\MerchantController::class, 'getTradeDetail']);
        Route::get('/merchant_stats.php', [App\Http\Controllers\Api\MerchantController::class, 'getStats']);
        Route::get('/merchant_info.php', [App\Http\Controllers\Api\MerchantController::class, 'getInfo']);

        // 工程师相关API
        Route::get('/engineer_workspace.php', [App\Http\Controllers\Api\EngineerController::class, 'getWorkspace']);

        // 销售人员相关API
        Route::get('/salesman_workspace.php', [App\Http\Controllers\Api\SalesmanController::class, 'getWorkspace']);

        // 代理商相关API
        Route::get('/agent_workspace.php', [App\Http\Controllers\Api\AgentController::class, 'getWorkspace']);

        // 机构相关API
        Route::get('/institution_workspace.php', [App\Http\Controllers\Api\InstitutionController::class, 'getWorkspace']);

        // 管理员相关API
        Route::get('/admin_workspace.php', [App\Http\Controllers\Api\AdminController::class, 'getWorkspace']);
    });
});

// 需要认证的接口
Route::middleware('auth:sanctum')->prefix('admin')->group(function () {
        // 管理后台API - 不需要认证（临时解决方案，与其他模块保持一致）
    Route::prefix('api')->withoutMiddleware(['auth:sanctum'])->group(function () {
        // 业务员管理
        Route::prefix('salesmen')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'index']);
            Route::post('/', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'store']);
            Route::get('/{id}', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'show']);
            Route::put('/{id}', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'update']);
            Route::delete('/{id}', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'destroy']);
            Route::get('/{id}/stats', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'stats']);
            Route::get('/{id}/team', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'team']);
            Route::get('/managers', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'getManagers']);
            Route::get('/available-users', [App\Http\Controllers\Admin\Api\V1\SalesmanController::class, 'getAvailableUsers']);
        });
    });
    
    // 系统管理
    /* 暂时注释掉不存在的控制器
    Route::prefix('system')->group(function () {
        Route::get('menu', [App\Http\Controllers\Admin\SystemController::class, 'getMenus']);
        Route::get('info', [App\Http\Controllers\Admin\SystemController::class, 'getSystemInfo']);
    });
    */



    // APP用户管理
    Route::prefix('app-users')->group(function () {
        Route::get('/', [AdminAppUserController::class, 'index']);
        Route::get('/{id}', [AdminAppUserController::class, 'show']);
        Route::post('/{id}/status', [AdminAppUserController::class, 'updateStatus']);

        // 用户设备管理API
        Route::prefix('/{userId}/devices')->group(function () {
            Route::get('/', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'index']);
            Route::post('/', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'store']);
        });
    });

    // 订单管理
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::get('/{id}', [OrderController::class, 'show']);
        Route::post('/{id}/status', [OrderController::class, 'updateStatus']);
        Route::post('/{id}/refund', [OrderController::class, 'refund']);
        Route::post('/{id}/logistics', [OrderController::class, 'updateLogistics']);
    });

    // 物流公司
    Route::prefix('logistics')->group(function () {
        Route::get('/', [LogisticsController::class, 'index']);
        Route::post('/', [LogisticsController::class, 'store']);
        Route::get('/{id}', [LogisticsController::class, 'show']);
        Route::post('/{id}', [LogisticsController::class, 'update']);
        Route::delete('/{id}', [LogisticsController::class, 'destroy']);
    });

    // 商品分类
    Route::prefix('product-categories')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'index']);
        Route::post('/', [App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'store']);
        Route::post('/upload-icon', [App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'uploadIcon']);
        Route::get('/{id}', [App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'show']);
        Route::post('/{id}', [App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'update']);
        Route::delete('/{id}', [App\Http\Controllers\Admin\Api\V1\ProductCategoryController::class, 'destroy']);
    });

    // 积分管理
    Route::prefix('points')->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\PointController::class, 'index']);
        Route::post('/award', [\App\Http\Controllers\Api\PointController::class, 'awardPoints']);
        Route::get('/records', [\App\Http\Controllers\Api\PointController::class, 'records']);
    });

    // 通知管理
    Route::prefix('notifications')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'index']);
        Route::get('/unread-count', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'unreadCount']);
        Route::get('/latest', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'latest']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'store']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'show']);
        Route::post('/{id}/read', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'markAsRead']);
        Route::post('/mark-all-read', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'markAllAsRead']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'destroy']);
        
        // 语音通知相关路由
        Route::get('/voice/list', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'list']);
        Route::post('/voice/played', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'markPlayed']);
        Route::post('/voice/batch-played', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'batchMarkPlayed']);
        Route::post('/voice/test', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'test']);
    });

    // Banner管理
    Route::prefix('banners')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'store']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'show']);
        Route::post('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'destroy']);
        Route::post('/{id}/status', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'updateStatus']);
        Route::post('/sort', [\App\Http\Controllers\Admin\Api\V1\BannerController::class, 'updateSort']);
    });

    // 商品管理
    Route::prefix('products')->group(function () {
        Route::get('/', [ProductController::class, 'index']);
        Route::post('/', [ProductController::class, 'store']);
        Route::get('/categories', [ProductController::class, 'getCategories']);
        Route::get('/{id}', [ProductController::class, 'show']);
        Route::post('/{id}', [ProductController::class, 'update']);
        Route::delete('/{id}', [ProductController::class, 'destroy']);
        Route::post('/{id}/status', [ProductController::class, 'updateStatus']);
        Route::post('/batch', [ProductController::class, 'batchOperation']);
    });

    // 文件上传
    Route::prefix('upload')->group(function () {
        Route::post('/image', [\App\Http\Controllers\Admin\Api\V1\UploadController::class, 'uploadImage']);
        Route::post('/file', [\App\Http\Controllers\Admin\Api\V1\UploadController::class, 'uploadFile']);
    });



    // 设备管理
    Route::prefix('devices')->group(function() {
        Route::get('/', [\App\Http\Controllers\Admin\DeviceController::class, 'apiIndex']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\DeviceController::class, 'apiShow']);
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\DeviceController::class, 'apiUpdateStatus']);
        Route::post('/', [\App\Http\Controllers\Admin\DeviceController::class, 'apiStore']);
        Route::put('/{id}', [\App\Http\Controllers\Admin\DeviceController::class, 'apiUpdate']);
        Route::get('/clients/list', [\App\Http\Controllers\Admin\DeviceController::class, 'apiClients']);
        // 获取设备制水趋势
        Route::get('/{id}/water-trend', [\App\Http\Controllers\Admin\DeviceController::class, 'apiWaterTrend']);
        // 获取设备制水历史
        Route::get('/{id}/water-history', [\App\Http\Controllers\Admin\DeviceController::class, 'apiWaterHistory']);
    });
});

/**
 * 公开API接口 - 商城模块
 */
Route::prefix('mall')->group(function () {
    // 商品分类
    Route::get('/categories', [ProductCategoryController::class, 'index']);

    // 商品列表
    Route::get('/products', [ProductController::class, 'index']);

    // 商品详情
    Route::get('/products/{id}', [ProductController::class, 'show']);

    // 获取商品分类列表
    Route::get('/product-categories', [ProductCategoryController::class, 'all']);
});

// 前端接口 - 不需要认证
Route::prefix('products')->group(function () {
    Route::get('/', [ProductController::class, 'index']);
    Route::get('/categories', [ProductController::class, 'getCategories']);
    Route::get('/{id}', [ProductController::class, 'show']);
});

Route::prefix('categories')->group(function () {
    Route::get('/', [ProductCategoryController::class, 'index']);
    Route::get('/all', [ProductCategoryController::class, 'all']);
    Route::get('/{id}', [ProductCategoryController::class, 'show']);
});

Route::prefix('upload')->group(function () {
    Route::post('/image', [\App\Http\Controllers\Admin\Api\V1\UploadController::class, 'uploadImage']);
    Route::post('/file', [\App\Http\Controllers\Admin\Api\V1\UploadController::class, 'uploadFile']);
});

// 短信管理API路由 - V1版本（前端调用）
Route::prefix('admin/v1/sms')->withoutMiddleware([\App\Http\Middleware\LegacyTokenMiddleware::class])->group(function () {
    // 短信日志
    Route::get('/logs', [\App\Http\Controllers\Admin\Api\SmsController::class, 'logs']);
    Route::get('/logs/{id}', [\App\Http\Controllers\Admin\Api\SmsController::class, 'logDetail']);
    Route::get('/logs/stats', [\App\Http\Controllers\Admin\Api\SmsController::class, 'logsStats']);
    Route::get('/statistics', [\App\Http\Controllers\Admin\Api\SmsController::class, 'logsStats']);
    
    // 验证码
    Route::get('/codes', [\App\Http\Controllers\Admin\Api\SmsController::class, 'codesList']);
    Route::get('/codes/stats', [\App\Http\Controllers\Admin\Api\SmsController::class, 'codesStats']);
});

// 设备相关API
Route::prefix('device')->middleware('auth:sanctum')->group(function () {
    // 获取用户绑定的设备列表
    Route::get('list', [\App\Http\Controllers\Api\DeviceController::class, 'userDevices']);

    // 获取设备详情
    Route::get('{id}', [\App\Http\Controllers\Api\DeviceController::class, 'deviceDetail'])->where('id', '[0-9a-zA-Z\-]+');

    // 获取设备状态
    Route::get('{id}/status', [\App\Http\Controllers\Api\DeviceController::class, 'deviceStatus'])->where('id', '[0-9a-zA-Z\-]+');

    // 绑定设备
    Route::post('bind', [\App\Http\Controllers\Api\DeviceController::class, 'bindDevice']);

    // 解绑设备
    Route::post('{id}/unbind', [\App\Http\Controllers\Api\DeviceController::class, 'unbindDevice'])->where('id', '[0-9a-zA-Z\-]+');
});

// 首页导航管理
Route::prefix('home-nav')->group(function () {
    Route::get('/', [HomeNavController::class, 'index']);
    Route::post('/', [HomeNavController::class, 'store']);
    Route::get('/{id}', [HomeNavController::class, 'show']);
    Route::put('/{id}', [HomeNavController::class, 'update']);
    Route::delete('/{id}', [HomeNavController::class, 'destroy']);
    Route::get('/active/list', [HomeNavController::class, 'getActiveNavItems']);
    Route::get('/icons/list', [HomeNavController::class, 'getVantIcons']);
});

/*
|--------------------------------------------------------------------------
| 订单和支付相关路由
|--------------------------------------------------------------------------
*/
Route::prefix('app/orders')->middleware('auth:sanctum')->group(function () {
    // 创建订单
    Route::post('/create', [App\Http\Controllers\Api\OrderController::class, 'createOrder']);
    // 查询订单状态
    Route::get('/status', [App\Http\Controllers\Api\OrderController::class, 'queryOrderStatus']);
    // 获取用户订单列表
    Route::get('/user', [App\Http\Controllers\Api\OrderController::class, 'getUserOrders']);
    // 取消订单
    Route::post('/cancel/{orderNo}', [App\Http\Controllers\Api\OrderController::class, 'cancelOrder']);
});

// 微信支付回调接口 (无需认证)
// 暂时注释掉不存在的控制器引用
// Route::post('/wechat/payment/notify', [App\Http\Controllers\Api\WechatPaymentController::class, 'notify']);

// 业务员管理API已移动到 routes/admin_api.php

// 用户设备管理（公共API，不需要认证）
Route::prefix('user-devices')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'store']);
    Route::get('/{id}', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'show']);
    Route::put('/{id}', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'update']);
    Route::delete('/{id}', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'destroy']);
    Route::post('/{id}/main', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'setMainDevice']);
    Route::post('/check', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'checkDevice']);
});

// 公共API：APP用户设备列表（不需要认证）
Route::prefix('admin/app-users')->group(function () {
    Route::get('/{userId}/devices', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'index']);
    Route::post('/{userId}/devices', [\App\Http\Controllers\Api\AppUserDeviceController::class, 'store']);
});

/*
|--------------------------------------------------------------------------
| 邀请函相关路由
|--------------------------------------------------------------------------
*/
// 前端访问的API接口
Route::prefix('invitation')->group(function () {
    // 获取邀请函详情
    Route::get('/{id}', [App\Http\Controllers\Api\InvitationController::class, 'getInvitation']);

    // 访问邀请函，记录访客信息
    Route::post('/{id}/join', [App\Http\Controllers\Api\InvitationController::class, 'join']);

    // 接受邀请，填写个人信息
    Route::post('/{id}/register', [App\Http\Controllers\Api\InvitationController::class, 'register']);
});

// 管理后台API接口
Route::middleware('auth:sanctum')->prefix('admin')->group(function () {
    // 邀请函管理
    Route::prefix('invitation')->group(function () {
        // 邀请函模板管理
        Route::get('/templates', [App\Http\Controllers\Admin\Web\InvitationController::class, 'index']);
        Route::post('/templates', [App\Http\Controllers\Admin\Web\InvitationController::class, 'store']);
        Route::get('/templates/{id}', [App\Http\Controllers\Admin\Web\InvitationController::class, 'show']);
        Route::put('/templates/{id}', [App\Http\Controllers\Admin\Web\InvitationController::class, 'update']);
        Route::delete('/templates/{id}', [App\Http\Controllers\Admin\Web\InvitationController::class, 'destroy']);
        Route::post('/templates/{id}/status', [App\Http\Controllers\Admin\Web\InvitationController::class, 'updateStatus']);

        // 访客管理
        Route::get('/templates/{templateId}/guests', [App\Http\Controllers\Admin\Web\InvitationController::class, 'guests']);
        Route::get('/templates/{templateId}/statistics', [App\Http\Controllers\Admin\Web\InvitationController::class, 'statistics']);

        // 上传资源
        Route::post('/upload/background-image', [App\Http\Controllers\Admin\Web\InvitationController::class, 'uploadBackgroundImage']);
        Route::post('/upload/background-music', [App\Http\Controllers\Admin\Web\InvitationController::class, 'uploadBackgroundMusic']);
    });
});

// 安装管理相关路由
Route::prefix('admin/installation')->middleware(['auth:sanctum'])->group(function () {
    // 安装预约管理
    Route::get('/booking', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'index']);
    Route::get('/booking/export', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'export']);
    Route::get('/booking/{id}', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'show']);
    Route::put('/booking/{id}', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'update']);

    // 派工程师功能
    Route::post('/booking/{id}/assign-engineer', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'assignEngineer']);

    // 获取可用工程师列表
    Route::get('/available-engineers', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'getAvailableEngineers']);

    // 安装统计
    Route::get('/statistics', [\App\Http\Controllers\Admin\Installation\StatisticsController::class, 'index']);
    Route::get('/statistics/export', [\App\Http\Controllers\Admin\Installation\StatisticsController::class, 'export']);
    Route::get('/get_statistics', [\App\Http\Controllers\Admin\Installation\StatisticsController::class, 'getStatistics']);
});

// V1版本API路由 - 点点够设备管理（修复路由前缀）
Route::prefix('admin/v1')->withoutMiddleware([\App\Http\Middleware\LegacyTokenMiddleware::class])->group(function () {
    Route::prefix('tapp-devices')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'index']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'show'])->where('id', '[0-9]+');
        Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'getAppUsers']);
        Route::post('/sync', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'syncData']);
        
        // 需要认证的路由
        Route::middleware('auth:sanctum')->group(function () {
            Route::put('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'update'])->where('id', '[0-9]+');
            Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'destroy'])->where('id', '[0-9]+');
            Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'updateStatus'])->where('id', '[0-9]+');
        });
    });
});

// 兼容旧版本的点点够设备API路由
Route::prefix('api/tapp-devices')->withoutMiddleware([\App\Http\Middleware\LegacyTokenMiddleware::class])->group(function () {
    Route::get('/', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'index']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'getAppUsers']);
    Route::post('/sync', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'syncData']);
    
    // 需要认证的路由
    Route::middleware('auth:sanctum')->group(function () {
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'update'])->where('id', '[0-9]+');
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'updateStatus'])->where('id', '[0-9]+');
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'destroy'])->where('id', '[0-9]+');
    });
});

// 完整路径的点点够设备API路由（前端使用的路径）- 修复认证问题
Route::group(['prefix' => 'Tapp/admin/public/api/tapp-devices', 'middleware' => []], function () {
    // 基本查询操作 - 不需要认证，完全公开
    Route::get('/', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'index']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'getAppUsers']);
    Route::post('/sync', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'syncData']);
    
    // 需要认证的路由 - 仅修改操作需要认证
    Route::middleware('auth:sanctum')->group(function () {
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'update'])->where('id', '[0-9]+');
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'updateStatus'])->where('id', '[0-9]+');
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'destroy'])->where('id', '[0-9]+');
    });
});

/*
 * WeChat Routes
 */
Route::group(['prefix' => 'wechat', 'middleware' => ['api']], function () {
    // 微信支付通知回调 - 不需要认证
    // 暂时注释掉不存在的控制器引用
    // Route::post('/payment-notify', 'Api\WechatPaymentController@notify');

    // 微信JSSDK配置 - 不需要认证
    Route::get('/jsconfig', [\App\Http\Controllers\Api\WechatJssdkController::class, 'getConfig']);
});

// 兼容旧的API路径 - 微信JSSDK配置
Route::get('/api/wechat/jsconfig.php', [\App\Http\Controllers\Api\WechatJssdkController::class, 'getConfig']);

// 直接访问根路径的微信JSSDK配置
Route::get('/wechat/jsconfig', [\App\Http\Controllers\Api\WechatJssdkController::class, 'getConfig']);

// 用户认证后可访问的路由
Route::group(['middleware' => ['api', 'auth:sanctum']], function () {
    // ... existing code ...

    /*
     * WeChat Payment Routes (Authenticated)
     */
    Route::group(['prefix' => 'wechat'], function () {
        // 创建微信支付订单
        // 暂时注释掉不存在的控制器引用
        // Route::post('/create-order', 'Api\WechatPaymentController@createOrder');
    });

    // ... existing code ...
});

// VIP分红相关API已移动到 routes/admin_api.php

// 管理后台API接口
// VIP分红管理API已移动到 routes/admin_api.php

// WeChat Payment Routes (Authenticated)
Route::middleware('auth:sanctum')->prefix('wechat/payment')->group(function () {
    Route::post('/create-order', [App\Http\Controllers\Common\Wechat\WechatPaymentController::class, 'createOrder']);
});

// 支付订单管理路由
Route::prefix('admin/payment')->middleware('auth:sanctum')->group(function () {
    Route::prefix('orders')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'index']);
        Route::get('/{id}', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'show']);
        Route::post('/{id}/ship', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'ship']);
        Route::post('/{id}/update-address', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'updateAddress']);
        Route::post('/{id}/cancel', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'cancel']);
        Route::post('/{id}/confirm', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'confirm']);
        Route::post('/batch-ship', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'batchShip']);
        Route::get('/export', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'export']);
        Route::get('/statistics', [\App\Http\Controllers\Admin\Payment\OrderController::class, 'statistics']);
    });
});

// VIP分红模块API路由 - 公开访问，不需要认证
Route::prefix('vip')->group(function () {
    // 获取VIP工作区数据
    Route::get('/workspace', [\App\Http\Controllers\Api\Vip\WorkspaceController::class, 'index']);
    // 获取VIP奖金池信息
    Route::get('/pool-info', [\App\Http\Controllers\Api\Vip\PoolInfoController::class, 'index']);
    // 获取VIP分红信息
    Route::get('/dividend-info', [\App\Http\Controllers\Api\Vip\DividendInfoController::class, 'index']);
    // 获取VIP团队信息
    Route::get('/team-info', [\App\Http\Controllers\Api\Vip\TeamInfoController::class, 'index']);
    // 获取VIP时间信息
    Route::get('/time-info', [\App\Http\Controllers\Api\Vip\TimeInfoController::class, 'index']);
    // 获取VIP团队成员
    Route::get('/team-members', [\App\Http\Controllers\Api\VipController::class, 'getTeamMembers']);
    // 获取VIP团队关系树（带层级信息）
    Route::get('/team-tree', [\App\Http\Controllers\Api\VipController::class, 'getTeamTree']);
    // 获取VIP分红记录
    Route::get('/dividends', [\App\Http\Controllers\Api\VipController::class, 'getDividends']);
    // 获取VIP奖励列表
    Route::get('/reward-list', [\App\Http\Controllers\Api\VipController::class, 'getRewardList']);
    // 获取VIP招募排行榜
    Route::get('/recruit-ranking', [\App\Http\Controllers\Api\VipController::class, 'getRecruitRanking']);
    // 结算分红
    Route::post('/settle-dividends', [\App\Http\Controllers\Api\VipController::class, 'settleDividends']);
    // 获取VIP个人资料信息（需要认证）
    Route::middleware('auth:sanctum')->get("/profile", [\App\Http\Controllers\Api\Vip\ProfileController::class, "index"]);
    Route::get('/dummy', [\App\Http\Controllers\Api\VipController::class, 'dummy']);
    // 团队信息和详情API使用数组语法，确保路由一致性
    Route::get('/team-info', [\App\Http\Controllers\Api\Vip\TeamInfoController::class, 'index']);
    Route::get('/team-detail', [\App\Http\Controllers\Api\Vip\TeamInfoController::class, 'getTeamDetail']); // 团队详情API
    // 获取团队统计API（包含团队总VIP数等信息）
    Route::get('/team-stats', [\App\Http\Controllers\Api\Vip\TeamInfoController::class, 'getTeamStats']);
});

// 为VIP模块添加API路由别名，将/api/路径映射到/vip/路径
Route::prefix('api')->group(function () {
    // 获取VIP工作区数据
    Route::get('/workspace', [\App\Http\Controllers\Api\Vip\WorkspaceController::class, 'index']);
    // 获取VIP奖金池信息
    Route::get('/pool-info', [\App\Http\Controllers\Api\Vip\PoolInfoController::class, 'index']);
    // 获取VIP分红信息
    Route::get('/dividend-info', [\App\Http\Controllers\Api\Vip\DividendInfoController::class, 'index']);
    // 获取VIP团队信息
    Route::get('/team-info', [\App\Http\Controllers\Api\Vip\TeamInfoController::class, 'index']);
    // 获取VIP时间信息
    Route::get('/time-info', [\App\Http\Controllers\Api\Vip\TimeInfoController::class, 'index']);
    // 获取VIP团队成员
    Route::get('/team-members', [\App\Http\Controllers\Api\VipController::class, 'getTeamMembers']);
    // 获取VIP团队关系树（带层级信息）
    Route::get('/team-tree', [\App\Http\Controllers\Api\VipController::class, 'getTeamTree']);
    // 获取VIP分红记录
    Route::get('/dividends', [\App\Http\Controllers\Api\VipController::class, 'getDividends']);
    // 获取VIP奖励列表
    Route::get('/reward-list', [\App\Http\Controllers\Api\VipController::class, 'getRewardList']);
    // 获取VIP招募排行榜
    Route::get('/recruit-ranking', [\App\Http\Controllers\Api\VipController::class, 'getRecruitRanking']);
    // 结算分红
    Route::post('/settle-dividends', [\App\Http\Controllers\Api\VipController::class, 'settleDividends']);
    // 获取VIP个人资料（需要认证）
    Route::middleware('auth:sanctum')->get('/profile', [\App\Http\Controllers\Api\Vip\ProfileController::class, 'index']);
});

// 为VIP分红模块添加标准RESTful API路由 - 公开访问，不需要认证
Route::prefix('api/vip')->name('api.vip.')->group(function () {
    // 获取VIP工作区数据
    Route::get('/workspace', [\App\Http\Controllers\Api\Vip\WorkspaceController::class, 'index'])->name('workspace');
    // 获取VIP奖金池信息
    Route::get('/pool-info', [\App\Http\Controllers\Api\Vip\PoolInfoController::class, 'index'])->name('pool-info');
    // 获取VIP分红信息
    Route::get('/dividend-info', [\App\Http\Controllers\Api\Vip\DividendInfoController::class, 'index'])->name('dividend-info');
    // 获取VIP团队信息
    Route::get('/team-info', [\App\Http\Controllers\Api\Vip\TeamInfoController::class, 'index'])->name('team-info');
    // 获取VIP时间信息
    Route::get('/time-info', [\App\Http\Controllers\Api\Vip\TimeInfoController::class, 'index'])->name('time-info');
    // 获取VIP团队成员
    Route::get('/team-members', [\App\Http\Controllers\Api\VipController::class, 'getTeamMembers'])->name('team-members');
    // 获取VIP团队关系树（带层级信息）
    Route::get('/team-tree', [\App\Http\Controllers\Api\VipController::class, 'getTeamTree'])->name('team-tree');
    // 获取VIP分红记录
    Route::get('/dividends', [\App\Http\Controllers\Api\VipController::class, 'getDividends'])->name('dividends');
    // 结算分红
    Route::post('/settle-dividends', [\App\Http\Controllers\Api\VipController::class, 'settleDividends'])->name('settle-dividends');
    // 获取VIP个人资料信息
    Route::get('/profile', [\App\Http\Controllers\Api\Vip\ProfileController::class, 'index'])->name('profile');
    // 获取VIP奖励列表
    Route::get('/reward-list', [\App\Http\Controllers\Api\VipController::class, 'getRewardList'])->name('reward-list');

    // 添加团队统计路由，确保正确指向TeamInfoController
    Route::get('/team-stats', [\App\Http\Controllers\Api\Vip\TeamInfoController::class, 'getTeamStats'])->name('team-stats');
});

// 商城订单模块API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('orders')->group(function () {
    // 创建订单
    Route::post('/create', [\App\Http\Controllers\Api\OrderController::class, 'create']);
    // 获取订单状态
    Route::get('/status', [\App\Http\Controllers\Api\OrderController::class, 'status']);
    // 获取用户订单列表
    Route::get('/user', [\App\Http\Controllers\Api\OrderController::class, 'getUserOrders']);
    // 取消订单
    Route::post('/cancel', [\App\Http\Controllers\Api\OrderController::class, 'cancel']);
    // 获取订单详情
    Route::get('/detail', [\App\Http\Controllers\Api\OrderController::class, 'detail']);
});

// 设备模块API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('device')->group(function () {
    // 获取用户设备列表
    Route::get('/list', [\App\Http\Controllers\Api\DeviceController::class, 'userDevices']);
    // 获取设备详情
    Route::get('/{id}', [\App\Http\Controllers\Api\DeviceController::class, 'deviceDetail'])->where('id', '[0-9]+');
    // 获取设备状态
    Route::get('/{id}/status', [\App\Http\Controllers\Api\DeviceController::class, 'deviceStatus'])->where('id', '[0-9]+');
});

// 积分模块API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('points')->group(function () {
    // 获取用户积分信息
    Route::get('/user', [\App\Http\Controllers\Api\PointController::class, 'getUserPoints']);
    // 获取用户积分记录
    Route::get('/user-records', [\App\Http\Controllers\Api\PointController::class, 'getUserPointRecords']);
});

// 商户模块API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('merchant')->group(function () {
    // 获取商户工作台数据
    Route::get('/workspace', [\App\Http\Controllers\Api\MerchantController::class, 'getWorkspace']);
    // 获取商户交易列表
    Route::get('/trade-list', [\App\Http\Controllers\Api\MerchantController::class, 'getTradeList']);
    // 获取商户交易详情
    Route::get('/trade-detail/{id}', [\App\Http\Controllers\Api\MerchantController::class, 'getTradeDetail'])->where('id', '[0-9]+');
    // 获取商户统计数据
    Route::get('/stats', [\App\Http\Controllers\Api\MerchantController::class, 'getStats']);
    // 获取商户信息
    Route::get('/info', [\App\Http\Controllers\Api\MerchantController::class, 'getInfo']);
});

// 工程师模块API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('engineer')->group(function () {
    // 获取工程师工作台数据
    Route::get('/workspace', [\App\Http\Controllers\Api\EngineerController::class, 'getWorkspace']);
});

// 销售人员模块API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('salesman')->group(function () {
    // 获取销售人员工作台数据
    Route::get('/workspace', [\App\Http\Controllers\Api\SalesmanController::class, 'getWorkspace']);
});

// 代理商模块API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('agent')->group(function () {
    // 获取代理商工作台数据
    Route::get('/workspace', [\App\Http\Controllers\Api\AgentController::class, 'getWorkspace']);
});

// 机构模块API路由 - 需要认证
Route::middleware('auth:sanctum')->prefix('institution')->group(function () {
    // 获取机构工作台数据
    Route::get('/workspace', [\App\Http\Controllers\Api\InstitutionController::class, 'getWorkspace']);
});

/*
|--------------------------------------------------------------------------
| 微信登录URL获取API - 统一RESTful风格
|--------------------------------------------------------------------------
|
| 以下路由处理微信登录URL获取，支持多种路径格式
| 所有路由都指向同一个控制器方法，确保一致性
| 所有路由都显式定义为公开访问，不需要任何中间件
|
*/
Route::withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])->group(function () {
    // 标准RESTful API路径
    Route::get('/api/auth/wechat/auth-url', [App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
        ->name('api.auth.wechat.auth-url');

    // 简化的RESTful API路径
    Route::get('/api/wechat/auth-url', [App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
        ->name('api.wechat.auth-url');

    // 不带api前缀的RESTful路径
    Route::get('/auth/wechat/auth-url', [App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
        ->name('auth.wechat.auth-url');

    Route::get('/wechat/auth-url', [App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
        ->name('wechat.auth-url');

    // 兼容旧版API路径
    Route::get('/api/wechat_login_url', [App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
        ->name('api.wechat_login_url');

    Route::get('/wechat_login_url', [App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
        ->name('wechat_login_url');

    Route::get('/wechat/login_url', [App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
        ->name('wechat.login_url');
});

/*
|--------------------------------------------------------------------------
| 管理后台API
|--------------------------------------------------------------------------
*/

// 管理后台 - 获取菜单数据
// 管理员菜单路由 - 使用正确的控制器从数据库获取菜单
Route::get('/admin/menus', [AdminMenuController::class, 'getMenus']);

// 用户角色检查API路由
Route::post('/user/check-roles', [App\Http\Controllers\Api\UserController::class, 'checkUserRoles'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.user.check-roles');

// 用户中心页面相关API路由
Route::prefix('user')->group(function () {
    // 获取用户信息 - 可选认证
    Route::get('/info', [App\Http\Controllers\Api\UserController::class, 'info'])
        ->name('api.user.info');
    
    // 需要认证的用户API
    Route::middleware('auth:sanctum')->group(function () {
        // 更新用户信息
        Route::post('/update-info', [App\Http\Controllers\Api\UserController::class, 'updateInfo'])
            ->name('api.user.update-info');
        
        // 更新用户密码
        Route::post('/update-password', [App\Http\Controllers\Api\UserController::class, 'updatePassword'])
            ->name('api.user.update-password');
        
        // 绑定手机号
        Route::post('/bind-phone', [AuthController::class, 'bindPhone'])
            ->name('api.user.bind-phone');
    });
});

// 微信授权相关路由 - 确保各种路径都能正确处理回调
Route::group([], function () {
    // 微信登录URL
    Route::get('/wechat/login-url', [\App\Http\Controllers\Api\WechatController::class, 'getLoginUrl'])
        ->withoutMiddleware(['auth:sanctum'])
        ->middleware(['throttle:60,1'])
        ->name('api.wechat.login-url-v2');

    // 微信登录回调 - 支持多种路径格式
    Route::any('/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, 'handleCallback'])
        ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
        ->name('api.wechat.callback.group2');
    
    Route::any('/auth/wechat/callback', [\App\Http\Controllers\Api\WechatController::class, 'handleCallback'])
        ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
        ->name('api.auth.wechat.callback.v2');
    
    Route::any('/callback', [\App\Http\Controllers\Api\WechatController::class, 'handleCallback'])
        ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
        ->name('api.callback');
});

// 临时修复：V1 APP用户管理API - 完全无需认证版本，与admins模块保持一致
Route::prefix('admin/v1')
    ->withoutMiddleware(['auth:sanctum', 'auth', 'api', 'web', 'throttle', 'csrf'])
    ->group(function () {
    // 测试路由 - 无需认证
    Route::get('test', function () {
        return response()->json([
            'code' => 200,
            'message' => 'V1 API工作正常（来自api.php）',
            'data' => [
                'timestamp' => now(),
                'version' => 'v1',
                'source' => 'api.php'
            ]
        ]);
    });
    
    // APP用户管理 - 临时无认证版本，与原生PHP API保持一致
    Route::get('app-users', function () {
        try {
            \Log::info('API路由app-users被调用（匿名函数版本）', [
                'request_params' => request()->all(),
                'user_agent' => request()->header('User-Agent'),
                'ip' => request()->ip()
            ]);

            $page = request()->get('page', 1);
            $perPage = request()->get('per_page', 15); // 改为默认15，与前端一致
            $keyword = request()->get('keyword', '');
            $role = request()->get('role', '');
            $status = request()->get('status', '');
            $dateStart = request()->get('date_start', '');
            $dateEnd = request()->get('date_end', '');
            $branchId = request()->get('branch_id', '');

            // 修正：查询app_users表，而不是users表
            $query = \DB::table('app_users');

            // 分支机构筛选 - 默认显示总部用户（branch_id为NULL）
            if (request()->has('branch_id') && !empty(request()->branch_id)) {
                $query->where('branch_id', request()->branch_id);
                \Log::info('应用分支机构筛选', ['branch_id' => request()->branch_id]);
            } else {
                $query->whereNull('branch_id');
                \Log::info('应用默认筛选：显示总部用户（branch_id为NULL）');
            }
            
            // 添加搜索功能 - 按照原生API的逻辑
            if ($keyword && trim($keyword) !== '') {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('email', 'like', "%{$keyword}%")
                      ->orWhere('wechat_nickname', 'like', "%{$keyword}%")
                      ->orWhere('id', 'like', "%{$keyword}%");
                });
            }
            
            // 角色筛选
            if ($role && trim($role) !== '') {
                switch($role) {
                    case 'pay_institution':
                        $query->where('is_pay_institution', 1);
                        break;
                    case 'water_purifier_user':
                        $query->where('is_water_purifier_user', 1);
                        break;
                    case 'engineer':
                        $query->where('is_engineer', 1);
                        break;
                    case 'water_purifier_agent':
                        $query->where('is_water_purifier_agent', 1);
                        break;
                    case 'pay_merchant':
                        $query->where('is_pay_merchant', 1);
                        break;
                    case 'vip':
                        $query->where('is_vip', 1);
                        break;
                    case 'sales':
                        $query->where('is_salesman', 1);
                        break;
                    case 'admin':
                        $query->where('is_admin', 1);
                        break;
                }
            }
            
            // 状态筛选
            if ($status && trim($status) !== '') {
                $query->where('status', $status);
            }
            
            // 日期范围筛选
            if ($dateStart && trim($dateStart) !== '') {
                $query->where('created_at', '>=', $dateStart . ' 00:00:00');
            }
            if ($dateEnd && trim($dateEnd) !== '') {
                $query->where('created_at', '<=', $dateEnd . ' 23:59:59');
            }
            
            // 获取总数
            $total = $query->count();
            \Log::info('查询总数', ['total_count' => $total]);

            // 分页查询
            $users = $query->orderBy('id', 'desc')
                           ->offset(($page - 1) * $perPage)
                           ->limit($perPage)
                           ->get();

            \Log::info('分页查询完成', [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'items_count' => count($users)
            ]);
            
            // 处理用户数据，添加角色信息（按照原生API逻辑）
            $processedUsers = [];
            foreach ($users as $user) {
                $userData = (array) $user;
                
                // 添加角色名称数组
                $roleNames = [];
                if ($userData['is_vip'] == 1) $roleNames[] = 'VIP会员';
                if ($userData['is_engineer'] == 1) $roleNames[] = '工程师';
                if ($userData['is_water_purifier_user'] == 1) $roleNames[] = '净水器用户';
                if ($userData['is_water_purifier_agent'] == 1) $roleNames[] = '净水器代理';
                if ($userData['is_pay_institution'] == 1) $roleNames[] = '支付机构';
                if ($userData['is_pay_merchant'] == 1) $roleNames[] = '支付商户';
                if ($userData['is_admin'] == 1) $roleNames[] = '管理员';
                if (empty($roleNames)) $roleNames[] = '普通用户';
                
                $userData['role_names'] = $roleNames;
                
                // 获取推荐人信息
                if (!empty($userData['referrer_id'])) {
                    $referrer = \DB::table('app_users')
                                  ->select('id', 'name', 'wechat_nickname')
                                  ->where('id', $userData['referrer_id'])
                                  ->first();
                    if ($referrer) {
                        $userData['referrer_name'] = $referrer->name ?: $referrer->wechat_nickname ?: '用户'.$referrer->id;
                    } else {
                        $userData['referrer_name'] = '未知用户';
                    }
                } else {
                    $userData['referrer_name'] = '点点够';
                }
                
                $processedUsers[] = $userData;
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $processedUsers,
                'total' => $total,
                'meta' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $total > 0 ? (($page - 1) * $perPage + 1) : null,
                    'to' => $total > 0 ? min($page * $perPage, $total) : null
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取APP用户列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    Route::get('app-users/{id}', function ($id) {
        try {
            // 修正：查询app_users表
            $user = \DB::table('app_users')->where('id', $id)->first();
            if (!$user) {
                return response()->json([
                    'code' => 404,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            $userData = (array) $user;
            
            // 添加角色信息
            $roleNames = [];
            if ($userData['is_vip'] == 1) $roleNames[] = 'VIP会员';
            if ($userData['is_engineer'] == 1) $roleNames[] = '工程师';
            if ($userData['is_water_purifier_user'] == 1) $roleNames[] = '净水器用户';
            if ($userData['is_water_purifier_agent'] == 1) $roleNames[] = '净水器代理';
            if ($userData['is_pay_institution'] == 1) $roleNames[] = '支付机构';
            if ($userData['is_pay_merchant'] == 1) $roleNames[] = '支付商户';
            if ($userData['is_admin'] == 1) $roleNames[] = '管理员';
            if (empty($roleNames)) $roleNames[] = '普通用户';
            
            $userData['role_names'] = $roleNames;
            
            // 获取推荐人信息
            if (!empty($userData['referrer_id'])) {
                $referrer = \DB::table('app_users')
                              ->select('id', 'name', 'wechat_nickname')
                              ->where('id', $userData['referrer_id'])
                              ->first();
                if ($referrer) {
                    $userData['referrer_name'] = $referrer->name ?: $referrer->wechat_nickname ?: '用户'.$referrer->id;
                } else {
                    $userData['referrer_name'] = '未知用户';
                }
            } else {
                $userData['referrer_name'] = '点点够';
            }
            
            return response()->json([
                'code' => 200,
                'message' => '获取用户信息成功',
                'data' => $userData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取用户信息失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    // 同步相关功能的临时实现
    Route::post('app-users/sync-roles', function () {
        try {
            return response()->json([
                'code' => 200,
                'message' => '同步功能暂未实现',
                'data' => [
                    'total' => 0,
                    'success' => 0,
                    'failed' => 0,
                    'duration' => 0
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '同步失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    Route::get('app-users/{id}/sync-roles', function ($id) {
        try {
            return response()->json([
                'code' => 200,
                'message' => '单用户同步功能暂未实现',
                'data' => null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '同步失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    Route::get('app-users/sync-progress', function () {
        try {
            return response()->json([
                'code' => 200,
                'message' => '获取同步进度成功',
                'data' => [
                    'progress' => 0,
                    'status' => 'idle'
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取同步进度失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    Route::post('app-users/sync-to-salesmen', function () {
        try {
            return response()->json([
                'code' => 200,
                'message' => '同步到业务员功能暂未实现',
                'data' => null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '同步失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    // 管理员管理路由 - 使用AdminController
    Route::get('admins', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'index']);
    Route::get('admins/{id}', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'show']);
    Route::post('admins', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'store']);
    Route::put('admins/{id}', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'update']);
    Route::delete('admins/{id}', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'destroy']);
    Route::put('admins/{id}/status', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'updateStatus']);
    Route::post('admins/{id}/reset-password', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'resetPassword']);
    Route::get('admins/roles', [\App\Http\Controllers\Admin\Api\V1\AdminController::class, 'roles']);
    
    // VIP分红管理路由 - 新版本优化系统
    Route::prefix('vip-dividends')->group(function () {
        // 分红批次管理
        Route::get('batches', [\App\Http\Controllers\VipDividendController::class, 'batches']);
        Route::post('batches', [\App\Http\Controllers\VipDividendController::class, 'createBatch']);
        Route::post('batches/{id}/calculate', [\App\Http\Controllers\VipDividendController::class, 'calculateDividend']);
        Route::get('batches/{id}/audit-logs', [\App\Http\Controllers\VipDividendController::class, 'auditLogs']);
        
        // 分红记录管理
        Route::get('records', [\App\Http\Controllers\VipDividendController::class, 'records']);
        
        // 分红统计
        Route::get('statistics', [\App\Http\Controllers\VipDividendController::class, 'statistics']);
        
        // 分红配置管理
        Route::get('configs', [\App\Http\Controllers\VipDividendController::class, 'configs']);
        
        // 月度奖金池管理API
        Route::get('overview-stats', [\App\Http\Controllers\VipDividendController::class, 'getOverviewStats']);
        Route::get('monthly-pools', [\App\Http\Controllers\VipDividendController::class, 'getMonthlyPools']);
        Route::post('calculate-monthly', [\App\Http\Controllers\VipDividendController::class, 'calculateMonthly']);
        Route::post('settle-monthly', [\App\Http\Controllers\VipDividendController::class, 'settleMonthly']);
        Route::get('month-detail/{month}', [\App\Http\Controllers\VipDividendController::class, 'getMonthDetail']);
        Route::get('export-monthly', [\App\Http\Controllers\VipDividendController::class, 'exportMonthly']);
    });
});

// VIP分红相关路由
Route::prefix('vip-dividends')->group(function () {
    // 计算分红
    Route::post('calculate', [App\Http\Controllers\VipDividendController::class, 'calculateDividends']);
    
    // 获取用户分红记录
    Route::get('user-dividends', [App\Http\Controllers\VipDividendController::class, 'getUserDividends']);
    
    // 获取分红统计
    Route::get('statistics', [App\Http\Controllers\VipDividendController::class, 'getStatistics']);
    
    // 获取用户分红预估
    Route::get('user-preview', [App\Http\Controllers\VipDividendController::class, 'getUserPreview']);
    
    // 获取分红配置
    Route::get('config', [App\Http\Controllers\VipDividendController::class, 'getConfig']);
    
    // 更新分红配置
    Route::put('config', [App\Http\Controllers\VipDividendController::class, 'updateConfig']);
    
    // 发放分红
    Route::post('distribute', [App\Http\Controllers\VipDividendController::class, 'distributeDividends']);
    
    // 获取分红排行榜
    Route::get('ranking', [App\Http\Controllers\VipDividendController::class, 'getRanking']);
});

// 工程师管理相关路由
Route::prefix('admin')->middleware(['auth:sanctum'])->group(function () {
    // 工程师基本管理
    Route::get('/engineers', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'index']);
    Route::post('/engineers', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'store']);
    Route::get('/engineers/{id}', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'show']);
    Route::put('/engineers/{id}', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'update']);
    Route::delete('/engineers/{id}', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'destroy']);

    // 获取可用工程师列表（用于派工）
    Route::get('/available-engineers', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'getAvailableEngineers']);

    // 从净水器数据库同步工程师数据
    Route::post('/engineers/sync-from-water-db', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'syncFromWaterDb']);
    Route::post('/engineers/update-installation-count', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'updateAllEngineersInstallationCount']);
    Route::get('/engineers/water-db-tables', [\App\Http\Controllers\Admin\Web\EngineerController::class, 'getWaterDbTables']);
    
    // 系统设置更新
    Route::post('/settings/update', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'updateSettings']);
    Route::post('/settings/upload-logo', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'uploadLogo']);
    Route::post('/settings/upload-favicon', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'uploadFavicon']);
});

// 临时公开favicon上传接口 - 用于解决认证问题
Route::post('/admin/settings/upload-favicon-temp', [\App\Http\Controllers\Admin\Api\V1\SettingController::class, 'uploadFavicon']);

// V1版本API路由 - 安装管理（修复认证问题）
Route::prefix('admin/installation')->group(function () {
    // 基本查询操作 - 不需要认证
    Route::get('/booking', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'index']);
    Route::get('/booking/export', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'export']);
    Route::get('/booking/{id}', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'show']);
    Route::get('/available-engineers', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'getAvailableEngineers']);
    Route::get('/statistics', [\App\Http\Controllers\Admin\Installation\StatisticsController::class, 'index']);
    Route::get('/statistics/export', [\App\Http\Controllers\Admin\Installation\StatisticsController::class, 'export']);
    Route::get('/get_statistics', [\App\Http\Controllers\Admin\Installation\StatisticsController::class, 'getStatistics']);
    
    // 需要认证的路由 - 仅修改操作需要认证
    Route::middleware('auth:sanctum')->group(function () {
        Route::put('/booking/{id}', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'update']);
        Route::post('/booking/{id}/assign-engineer', [\App\Http\Controllers\Admin\Installation\BookingController::class, 'assignEngineer']);
    });
});

/*
|--------------------------------------------------------------------------
| 完全公开的API路由 - 不使用任何中间件
|--------------------------------------------------------------------------
*/

// 点点够设备API - 完全公开，不使用任何中间件
Route::group(['prefix' => 'Tapp/admin/public/api/tapp-devices'], function () {
    Route::get('/', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'index']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'getAppUsers']);
    Route::post('/sync', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'syncData']);
    Route::get('/stats', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getStats']);
    Route::get('/export', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'export']);
    Route::post('/batch', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'batchOperate']);
    Route::get('/{id}/water-logs', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getWaterLogs'])->where('id', '[0-9]+');
    Route::get('/{id}/filters', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getFilters'])->where('id', '[0-9]+');
    Route::get('/{id}/online-status', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getOnlineStatus'])->where('id', '[0-9]+');
    Route::get('/{id}/alerts', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getAlerts'])->where('id', '[0-9]+');
});

// 兼容版本的点点够设备API - 完全公开
Route::group(['prefix' => 'api/tapp-devices'], function () {
    Route::get('/', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'index']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'getAppUsers']);
    Route::post('/sync', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'syncData']);
});

// V1版本的点点够设备API - 完全公开
Route::group(['prefix' => 'api/admin/v1/tapp-devices'], function () {
    Route::get('/', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'index']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'getAppUsers']);
    Route::post('/sync', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'syncData']);
});Route::get("/test-no-middleware", function () { return response()->json(["code" => 0, "message" => "测试成功", "data" => ["timestamp" => now()]]); });
Route::get("/test-tapp-devices-direct", function () { return response()->json(["code" => 0, "message" => "点点够设备API测试成功", "data" => ["test" => true]]); });

// 添加一个简单的设备API测试路由
Route::get("/test-tapp-devices-controller", function () {
    try {
        $controller = new \App\Http\Controllers\Admin\Api\TappDeviceApiController();
        $request = \Illuminate\Http\Request::create('/test', 'GET', ['page' => 1, 'per_page' => 10]);
        $response = $controller->index($request);
        return $response;
    } catch (Exception $e) {
        return response()->json([
            'code' => 500,
            'message' => '控制器测试失败',
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
    }
});
Route::get("/test-tapp-controller", [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, "index"]);

// 语音通知API路由 - 修复路径匹配问题，移除认证中间件
Route::prefix('api/test-voice')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    Route::get('/list', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'list']);
    Route::post('/played', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'markPlayed']);
    Route::post('/batch-played', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'batchMarkPlayed']);
    Route::post('/test', [\App\Http\Controllers\Admin\Api\V1\VoiceNotificationController::class, 'test']);
});

// 手机端API - V1版本（公开接口，不需要认证）
Route::prefix('mobile/v1')->group(function () {
    // VIP相关路由
    Route::prefix('vip')->group(function () {
        // 获取VIP团队信息 - 支持GET和POST方法
        Route::match(['get', 'post'], 'team-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getTeamInfo']);
        
        // 获取VIP奖金池信息 - 支持GET和POST方法
        Route::match(['get', 'post'], 'pool-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getPoolInfo']);
        
        // 获取VIP分红信息 - 支持GET和POST方法
        Route::match(['get', 'post'], 'dividend-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getDividendInfo']);
    });

    // 取水点相关API
    Route::prefix('water-points')->group(function () {
        // 获取取水点列表
        Route::get('/', [WaterPointController::class, 'index']);
        
        // 获取取水点详情
        Route::get('/{id}', [WaterPointController::class, 'show']);
        
        // 获取附近取水点
        Route::get('/nearby/search', [WaterPointController::class, 'nearby']);
        
        // 收藏/取消收藏取水点（需要认证）
        Route::post('/favorite/toggle', [WaterPointController::class, 'toggleFavorite'])->middleware('auth:sanctum');
    });
});

// 手机端V1 VIP API - 完全公开，不使用任何中间件
Route::group(['prefix' => 'mobile/v1/vip', 'middleware' => []], function () {
    // 获取VIP团队信息
    Route::match(['get', 'post'], 'team-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getTeamInfo']);
    
    // 获取VIP奖金池信息
    Route::match(['get', 'post'], 'pool-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getPoolInfo']);
    
    // 获取VIP分红信息
    Route::match(['get', 'post'], 'dividend-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getDividendInfo']);
});

// 管理后台V1版本API路由已在RouteServiceProvider中注册，此处不再重复注册

// V1版本微信回调路由 - 必须在主路由文件中定义，确保路径正确
Route::match(['get', 'post'], 'admin/v1/auth/wechat/bind-callback', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'wechatBindCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.admin.v1.auth.wechat.bind-callback');

// V1版本微信登录回调路由
Route::match(['get', 'post'], 'admin/v1/auth/wechat/login-callback', [\App\Http\Controllers\Admin\Api\V1\AuthController::class, 'wechatLoginCallback'])
    ->withoutMiddleware(['auth:sanctum', 'api', 'web', 'throttle', 'csrf'])
    ->name('api.admin.v1.auth.wechat.login-callback');

Route::group(['middleware' => []], function () {
    Route::post('mobile/v1/vip/team-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getTeamInfo']);
    Route::post('mobile/v1/vip/pool-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getPoolInfo']);
    Route::post('mobile/v1/vip/dividend-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getDividendInfo']);
    
    Route::get('mobile/v1/vip/team-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getTeamInfo']);
    Route::get('mobile/v1/vip/pool-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getPoolInfo']);
    Route::get('mobile/v1/vip/dividend-info', [\App\Http\Controllers\Mobile\Api\V1\VipController::class, 'getDividendInfo']);
});

// 完全公开的点点够设备API路由（不使用任何中间件组）
Route::group(['prefix' => 'Tapp/admin/public/api/tapp-devices', 'middleware' => []], function () {
    // 基本查询操作 - 完全不需要认证
    Route::get('/', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'index']);
    Route::get('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/app-users/list', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'getAppUsers']);
    Route::post('/sync', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'syncData']);
    Route::get('/stats', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getStats']);
    Route::get('/export', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'export']);
    Route::post('/batch', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'batchOperate']);
    Route::get('/{id}/water-logs', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getWaterLogs'])->where('id', '[0-9]+');
    Route::get('/{id}/filters', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getFilters'])->where('id', '[0-9]+');
    Route::get('/{id}/online-status', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getOnlineStatus'])->where('id', '[0-9]+');
    Route::get('/{id}/alerts', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'getAlerts'])->where('id', '[0-9]+');
    
    // 需要认证的路由 - 仅修改操作需要认证
    Route::middleware(['api', 'auth:sanctum'])->group(function () {
        Route::put('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'update'])->where('id', '[0-9]+');
        Route::put('/{id}/status', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'updateStatus'])->where('id', '[0-9]+');
        Route::delete('/{id}', [\App\Http\Controllers\Admin\Api\TappDeviceApiController::class, 'destroy'])->where('id', '[0-9]+');
        Route::put('/{id}/filters', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'updateFilters'])->where('id', '[0-9]+');
        Route::post('/{id}/filters/reset', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'resetFilter'])->where('id', '[0-9]+');
        Route::post('/{id}/control', [\App\Http\Controllers\Admin\Web\TappDeviceController::class, 'control'])->where('id', '[0-9]+');
    });
});

/*
|--------------------------------------------------------------------------
| 管理后台V1 API路由 - 菜单和通知（统一使用V1版本）
|--------------------------------------------------------------------------
*/

// 管理员菜单API - V1版本（公开访问）
Route::prefix('admin/api/admin')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    Route::get('/menus', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'index']);
    Route::post('/menus', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'store']);
    Route::get('/menus/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'show']);
    Route::put('/menus/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'update']);
    Route::delete('/menus/{id}', [\App\Http\Controllers\Admin\Api\V1\MenuController::class, 'destroy']);
});

// 通知API - V1版本（公开访问）
Route::prefix('admin/api')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    Route::get('/notifications', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'index']);
    Route::post('/notifications', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'store']);
    Route::get('/notifications/{id}', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'show']);
    Route::put('/notifications/{id}', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'update']);
    Route::delete('/notifications/{id}', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'destroy']);
    Route::get('/notifications/unread-count', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'unreadCount']);
    Route::post('/notifications/{id}/mark-read', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'markAsRead']);
    Route::post('/notifications/mark-all-read', [\App\Http\Controllers\Admin\Api\V1\NotificationController::class, 'markAllAsRead']);
});

// 注释：分支机构管理API已移至admin_api_v1.php，使用认证中间件
// 菜单和通知API现在也在admin_api_v1.php中统一管理，需要认证

/*
|--------------------------------------------------------------------------
| 系统管理API路由 - 登录日志等系统功能
|--------------------------------------------------------------------------
*/

// 系统管理API - 登录日志（公开访问，不需要认证）
Route::prefix('api/system')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 登录日志管理
    Route::get('/login-logs', [\App\Http\Controllers\Admin\Web\LoginLogController::class, 'index']);
    Route::get('/login-logs/{id}', [\App\Http\Controllers\Admin\Web\LoginLogController::class, 'show']);
    Route::get('/login-logs/statistics', [\App\Http\Controllers\Admin\Web\LoginLogController::class, 'statistics']);
    Route::delete('/login-logs', [\App\Http\Controllers\Admin\Web\LoginLogController::class, 'destroy']);
    Route::post('/login-logs/cleanup', [\App\Http\Controllers\Admin\Web\LoginLogController::class, 'cleanup']);
});

// 移除所有原生API路由，统一使用V1版本
// 原生API路由已被V1版本替代，不再需要

/*
|--------------------------------------------------------------------------
| 盛付通管理API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 盛付通API - V1版本（公开访问，不需要认证）
Route::prefix('admin/v1/shengfutong')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 控制面板
    Route::get('/dashboard/stats', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getDashboardStats']);
    Route::get('/dashboard/trend', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getTrendData']);
    Route::get('/dashboard/payment-stats', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getPaymentStats']);
    Route::get('/dashboard/merchant-ranking', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getMerchantRanking']);
    Route::get('/dashboard/channel-ranking', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getChannelRanking']);
    Route::get('/dashboard/realtime-transactions', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getRealtimeTransactions']);
    
    // 数据管理
    Route::get('/daily-data', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getDailyData']);
    Route::get('/monthly-data', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getMonthlyData']);
    
    // 日数据管理 (V1 API)
    Route::get('/daily-data/list', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getDailyList']);
    Route::post('/daily-data/calculate', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'calculateDaily']);
    Route::post('/daily-data/batch-calculate', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'batchCalculateDaily']);
    Route::post('/daily-data/check-data', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'checkData']);
    
    // 月数据管理 (V1 API)
    Route::get('/monthly-data/list', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getMonthlyData']);
    Route::post('/monthly-data/calculate', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'calculateMonthly']);
    Route::post('/monthly-data/check-data', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'checkMonthlyData']);
    
    // 数据上传 - 使用uploadData方法
    Route::post('/upload', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'uploadData']);
    
    // 数据导出
    Route::post('/export/daily', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'exportDailyData']);
    Route::post('/export/monthly', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'exportMonthlyData']);
    
    // 余额管理
    Route::get('/balance/data', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getBalanceData']);
    Route::get('/balance/flow', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getBalanceFlow']);
    Route::post('/balance/withdraw', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'withdrawApply']);
    Route::post('/balance/recharge', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'recharge']);
    
    // 提现管理
    Route::get('/withdrawal/list', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getWithdrawalList']);
    Route::get('/withdrawal/statistics', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getWithdrawalStatistics']);
    Route::post('/withdrawal/audit', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'auditWithdrawal']);
    Route::get('/withdrawal/alipay/balance', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getAlipayBalance']);
    
    // 文件上传管理
    Route::get('/upload/logs', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getUploadLogs']);
    Route::delete('/upload/logs/{logId}', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'deleteUploadLog']);
    
    // 机构汇总管理
    Route::get('/institution-summary/list', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getInstitutionSummary']);
    Route::post('/institution-summary/sync', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'syncInstitutionData']);
    Route::get('/institution-summary/sync-status', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getInstitutionSyncStatus']);
    
    // 余额管理
    Route::get('/balance/list', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'getBalanceList']);
    Route::post('/balance/sync', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'syncBalanceData']);
    Route::post('/balance/batch-to-non-withdrawable', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'batchToNonWithdrawable']);
    Route::post('/balance/batch-to-withdrawable', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'batchToWithdrawable']);
    Route::post('/balance/manage-commission-to-withdrawable', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'manageCommissionToWithdrawable']);
    Route::post('/balance/adjust', [\App\Http\Controllers\Admin\Api\V1\ShengfutongController::class, 'adjustBalance']);
});

// 引入盛付通测试路由
if (file_exists(__DIR__ . '/test_shengfutong.php')) {
    require __DIR__ . '/test_shengfutong.php';
}

// 引入商城测试路由
if (file_exists(__DIR__ . '/test_mall.php')) {
    require __DIR__ . '/test_mall.php';
}

/*
|--------------------------------------------------------------------------
| 机构汇总管理API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 机构汇总管理API - V1版本（公开访问，不需要认证）
Route::prefix('admin/v1/institution-summary')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 获取机构汇总列表
    Route::get('/', [InstitutionSummaryController::class, 'index']);
    
    // 同步机构数据
    Route::post('/sync', [InstitutionSummaryController::class, 'syncData']);
    
    // 获取同步状态
    Route::get('/sync-status', [InstitutionSummaryController::class, 'getSyncStatus']);
});

/*
|--------------------------------------------------------------------------
| 余额管理API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 余额管理API - V1版本（公开访问，不需要认证）
Route::prefix('admin/v1/balance-management')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 获取余额列表
    Route::get('/list', [BalanceManagementController::class, 'getBalanceList']);
    
    // 同步分润到待结算余额
    Route::post('/add', [BalanceManagementController::class, 'addBalance']);
    
    // 批量转入不可提现余额
    Route::post('/batch-to-non-withdrawable', [BalanceManagementController::class, 'batchToNonWithdrawable']);
    
    // 批量转入可提现余额
    Route::post('/batch-to-withdrawable', [BalanceManagementController::class, 'batchToWithdrawable']);
    
    // 调整余额
    Route::post('/update', [BalanceManagementController::class, 'updateBalance']);
});

/*
|--------------------------------------------------------------------------
| 提现审核管理API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 提现审核管理API - V1版本（修复路径匹配问题，移除认证中间件）
Route::prefix('api/admin/v1/withdrawal-audit')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 获取提现列表
    Route::get('/withdrawals', [WithdrawalAuditController::class, 'getWithdrawals']);
    
    // 审核提现申请
    Route::post('/audit', [WithdrawalAuditController::class, 'auditWithdrawal']);
    
    // 获取统计数据
    Route::get('/statistics', [WithdrawalAuditController::class, 'getStatistics']);
    
    // 检查新订单
    Route::get('/check-new', [WithdrawalAuditController::class, 'checkNewWithdrawals']);
    
    // 获取可提现余额
    Route::get('/balance/{institutionId}', [WithdrawalAuditController::class, 'getWithdrawableBalance']);
});

/*
|--------------------------------------------------------------------------
| 定时任务管理API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 定时任务管理API - V1版本（修复路径匹配问题，移除认证中间件）
Route::prefix('api/admin/v1/scheduled-tasks')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 统计信息 - 必须放在前面，避免被 /{id} 路由匹配
    Route::get('stats', [ScheduledTaskController::class, 'getStats']);
    
    // 基础CRUD操作
    Route::get('/', [ScheduledTaskController::class, 'index']);
    Route::post('/', [ScheduledTaskController::class, 'store']);
    Route::get('{id}', [ScheduledTaskController::class, 'show'])->where('id', '[0-9]+');
    Route::put('{id}', [ScheduledTaskController::class, 'update'])->where('id', '[0-9]+');
    Route::delete('{id}', [ScheduledTaskController::class, 'destroy'])->where('id', '[0-9]+');
    
    // 任务操作
    Route::post('{id}/toggle', [ScheduledTaskController::class, 'toggleStatus'])->where('id', '[0-9]+');
    Route::post('{id}/run', [ScheduledTaskController::class, 'runTask'])->where('id', '[0-9]+');
    Route::get('{id}/logs', [ScheduledTaskController::class, 'getLogs'])->where('id', '[0-9]+');
});

/*
|--------------------------------------------------------------------------
| 商城系统API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 商城管理API - V1版本（需要认证）
Route::prefix('admin/v1/mall')->middleware(['auth:sanctum'])->group(function () {
    // 商品管理
    Route::apiResource('goods', \App\Http\Controllers\Admin\Api\V1\Mall\GoodsController::class);
    Route::put('goods/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\GoodsController::class, 'updateStatus']);
    
    // 分类管理
    Route::apiResource('categories', \App\Http\Controllers\Admin\Api\V1\Mall\CategoryController::class);
    Route::put('categories/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\CategoryController::class, 'updateStatus']);
    
    // 订单管理
    Route::apiResource('orders', \App\Http\Controllers\Admin\Api\V1\Mall\OrderController::class);
    Route::put('orders/{id}/status', [\App\Http\Controllers\Admin\Api\V1\Mall\OrderController::class, 'updateStatus']);
    Route::post('orders/{id}/ship', [\App\Http\Controllers\Admin\Api\V1\Mall\OrderController::class, 'ship']);
    Route::post('orders/{id}/refund', [\App\Http\Controllers\Admin\Api\V1\Mall\OrderController::class, 'refund']);
    Route::get('orders/statistics', [\App\Http\Controllers\Admin\Api\V1\Mall\OrderController::class, 'statistics']);
});

// 商城手机端API - V1版本（公开访问，不需要认证）
Route::prefix('mobile/v1/mall')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 商品相关
            Route::get('goods', [\App\Http\Controllers\Mobile\Api\V1\goods\GoodsController::class, 'index']);
        Route::get('goods/{id}', [\App\Http\Controllers\Mobile\Api\V1\goods\GoodsController::class, 'show']);
        Route::get('goods/category/{categoryId}', [\App\Http\Controllers\Mobile\Api\V1\goods\GoodsController::class, 'getByCategory']);
    
    // 分类相关
    Route::get('categories', [\App\Http\Controllers\Mobile\Api\V1\Mall\CategoryController::class, 'index']);
    Route::get('categories/tree', [\App\Http\Controllers\Mobile\Api\V1\Mall\CategoryController::class, 'tree']);
    
    // 订单相关（需要用户认证）
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::post('orders', [\App\Http\Controllers\Mobile\Api\V1\Mall\OrderController::class, 'store']);
        Route::get('orders', [\App\Http\Controllers\Mobile\Api\V1\Mall\OrderController::class, 'index']);
        Route::get('orders/{id}', [\App\Http\Controllers\Mobile\Api\V1\Mall\OrderController::class, 'show']);
        Route::post('orders/{id}/pay', [\App\Http\Controllers\Mobile\Api\V1\Mall\OrderController::class, 'pay']);
        Route::post('orders/{id}/cancel', [\App\Http\Controllers\Mobile\Api\V1\Mall\OrderController::class, 'cancel']);
        Route::post('orders/{id}/confirm', [\App\Http\Controllers\Mobile\Api\V1\Mall\OrderController::class, 'confirm']);
    });
});

// 引入Mobile API V1路由
Route::prefix("mobile/v1")->group(function () {
    require __DIR__ . "/mobile_api_v1.php";
    require __DIR__ . "/mobile_api_additional.php";
});

// 引入Admin API V1路由 - 移除认证中间件以支持V1 API
Route::prefix("admin/v1")->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    require __DIR__ . "/admin_api_v1.php";
});

require __DIR__ . '/api_system.php';



/*
|--------------------------------------------------------------------------
| 微信自动回复API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 微信自动回复API - V1版本（需要认证）
Route::prefix('admin/v1/branches/{branchId}/wechat/auto-reply')->middleware(['auth:sanctum'])->group(function () {
    // 自动回复规则列表
    Route::get('/', [\App\Http\Controllers\Platform\AutoReplyController::class, 'index']);
    
    // 自动回复规则详情
    Route::get('/{id}', [\App\Http\Controllers\Platform\AutoReplyController::class, 'show']);
    
    // 创建自动回复规则
    Route::post('/', [\App\Http\Controllers\Platform\AutoReplyController::class, 'store']);
    
    // 更新自动回复规则
    Route::put('/{id}', [\App\Http\Controllers\Platform\AutoReplyController::class, 'update']);
    
    // 删除自动回复规则
    Route::delete('/{id}', [\App\Http\Controllers\Platform\AutoReplyController::class, 'destroy']);
    
    // 批量删除自动回复规则
    Route::post('/batch-delete', [\App\Http\Controllers\Platform\AutoReplyController::class, 'batchDestroy']);
    
    // 切换规则状态
    Route::patch('/{id}/status', [\App\Http\Controllers\Platform\AutoReplyController::class, 'updateStatus']);
    
    // 检查关键字冲突
    Route::post('/check-keyword', [\App\Http\Controllers\Platform\AutoReplyController::class, 'checkKeyword']);
    
    // 获取特殊消息回复设置
    Route::get('/special', [\App\Http\Controllers\Platform\AutoReplyController::class, 'getSpecialReplies']);
    
    // 更新特殊消息回复设置
    Route::post('/special', [\App\Http\Controllers\Platform\AutoReplyController::class, 'updateSpecialReply']);
});

/*
|--------------------------------------------------------------------------
| 微信粉丝管理API路由 - Platform版本
|--------------------------------------------------------------------------
*/

// 微信粉丝管理API - Platform版本（临时移除认证用于测试）
Route::prefix('admin/v1/branches/{branchId}/wechat/fans')->group(function () {
    // 粉丝管理
    Route::get('/', [FansController::class, 'index']);
    Route::get('/stats', [FansController::class, 'stats']);
    Route::get('/group-stats', [FansController::class, 'groupStats']);
    Route::get('/tag-stats', [FansController::class, 'tagStats']);
    Route::get('/growth-trend', [FansController::class, 'growthTrend']);
    Route::post('/export', [FansController::class, 'export']);
        Route::post('/sync', [FansController::class, 'sync']);
    Route::get('/sync-history', [FansController::class, 'syncHistory']);
      Route::get('/{id}', [FansController::class, 'show']);
    Route::put('/{id}', [FansController::class, 'update']);
    Route::delete('/{id}', [FansController::class, 'destroy']);
    Route::post('/batch-delete', [FansController::class, 'batchDelete']);
    Route::post('/batch-move-group', [FansController::class, 'batchMoveGroup']);
    Route::post('/batch-add-tags', [FansController::class, 'batchAddTags']);
    Route::post('/batch-remove-tags', [FansController::class, 'batchRemoveTags']);
});

// 微信粉丝分组管理API - Platform版本（临时移除认证用于测试）
Route::prefix('admin/v1/branches/{branchId}/wechat/fan-groups')->group(function () {
    Route::get('/', [FanGroupController::class, 'index']);
    Route::post('/', [FanGroupController::class, 'store']);
    Route::get('/options', [FanGroupController::class, 'options']);
    Route::get('/{id}', [FanGroupController::class, 'show']);
    Route::put('/{id}', [FanGroupController::class, 'update']);
    Route::delete('/{id}', [FanGroupController::class, 'destroy']);
    Route::post('/batch-delete', [FanGroupController::class, 'batchDelete']);
    Route::put('/{id}/status', [FanGroupController::class, 'updateStatus']);
    Route::post('/{id}/update-count', [FanGroupController::class, 'updateCount']);
});

// 微信粉丝标签管理API - Platform版本（临时移除认证用于测试）
Route::prefix('admin/v1/branches/{branchId}/wechat/fan-tags')->group(function () {
    Route::get('/', [FanTagController::class, 'index']);
    Route::post('/', [FanTagController::class, 'store']);
    Route::get('/options', [FanTagController::class, 'options']);
    Route::get('/preset-colors', [FanTagController::class, 'presetColors']);
    Route::get('/{id}', [FanTagController::class, 'show']);
    Route::put('/{id}', [FanTagController::class, 'update']);
    Route::delete('/{id}', [FanTagController::class, 'destroy']);
    Route::post('/batch-delete', [FanTagController::class, 'batchDelete']);
    Route::put('/{id}/status', [FanTagController::class, 'updateStatus']);
    Route::post('/{id}/update-usage', [FanTagController::class, 'updateUsageCount']);
});

// 注意：已移除重复的微信粉丝管理路由，统一使用Platform版本

/*
|--------------------------------------------------------------------------
| 微信素材管理API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 微信素材管理API - V1版本（临时移除认证用于测试）
Route::prefix('admin/v1/branches/{branchId}/wechat/materials')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 素材列表（按类型）
    Route::get('/{type?}', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMaterialController::class, 'index'])->where('type', 'news|image|voice|video');
    
    // 素材详情
    Route::get('/{type}/{mediaId}', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMaterialController::class, 'show']);
    
    // 上传素材
    Route::post('/upload/{type}', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMaterialController::class, 'upload']);
    
    // 删除素材
    Route::delete('/{type}/{mediaId}', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMaterialController::class, 'destroy']);
    
    // 创建图文消息
    Route::post('/news', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMaterialController::class, 'createNews']);
    
    // 更新图文消息
    Route::put('/news/{mediaId}', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMaterialController::class, 'updateNews']);
    
    // 同步素材
    Route::post('/sync', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMaterialController::class, 'sync']);
});

/*
|--------------------------------------------------------------------------
| 微信消息群发API路由 - V1版本
|--------------------------------------------------------------------------
*/

// 微信消息群发API - V1版本（临时移除认证用于测试）
Route::prefix('admin/v1/branches/{branchId}/wechat/mass-message')->withoutMiddleware(['auth:sanctum', 'auth', 'api'])->group(function () {
    // 群发统计
    Route::get('/stats', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'getStats']);
    
    // 群发消息列表
    Route::get('/', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'index']);
    
    // 群发消息详情
    Route::get('/{msgId}', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'show']);
    
    // 创建群发消息
    Route::post('/', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'store']);
    
    // 更新群发消息
    Route::put('/{msgId}', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'update']);
    
    // 发送群发消息
    Route::post('/{msgId}/send', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'send']);
    
    // 删除群发消息
    Route::delete('/{msgId}', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'destroy']);
    
    // 获取群发状态
    Route::get('/{msgId}/status', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'getStatus']);
    
    // 获取发送对象选项（分组、标签）
    Route::get('/target-options', [\App\Http\Controllers\Admin\Api\V1\BranchWechatMassMessageController::class, 'getTargetOptions']);
});

// 微信授权账号接口
Route::prefix('api/wechat')->group(function () {
    Route::get('/authorized-accounts', function () {
        $accounts = \DB::table('wechat_authorized_accounts')
            ->select('id', 'nick_name', 'authorizer_appid', 'head_img', 'service_type_info', 'verify_type_info')
            ->orderBy('id')
            ->get();
            
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $accounts
        ]);
    });
});

// 测试路由 - 无认证
Route::get('test/tapp-devices', function() {
    return response()->json([
        'code' => 0,
        'message' => 'API正常工作',
        'data' => [
            'total_devices' => 100,
            'online_devices' => 80,
            'statistics' => [
                'total_devices' => 100,
                'online_devices' => 80,
                'offline_devices' => 20,
                'warning_devices' => 5
            ]
        ]
    ]);
});




// ===============================================
// APP专用API路由 (v1.0.0)
// 路径前缀: /api/app/v1
// 专为iOS/Android APP设计
// ===============================================
Route::group(["prefix" => "app/v1"], function () {
    require __DIR__ . "/app_api_v1.php";
});
