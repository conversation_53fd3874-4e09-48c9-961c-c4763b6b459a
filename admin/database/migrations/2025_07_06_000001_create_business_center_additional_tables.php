<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 商户交易记录表
        if (!Schema::hasTable('merchant_transactions')) {
            Schema::create('merchant_transactions', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('merchant_id')->comment('商户ID');
                $table->string('transaction_id')->unique()->comment('交易流水号');
                $table->string('order_id')->nullable()->comment('订单号');
                $table->decimal('amount', 10, 2)->comment('交易金额');
                $table->decimal('fee_amount', 10, 2)->default(0)->comment('手续费金额');
                $table->decimal('actual_amount', 10, 2)->comment('实际到账金额');
                $table->enum('type', ['payment', 'refund', 'settlement'])->comment('交易类型');
                $table->enum('status', ['pending', 'success', 'failed', 'cancelled'])->default('pending')->comment('交易状态');
                $table->string('payment_method')->nullable()->comment('支付方式');
                $table->string('customer_name')->nullable()->comment('客户姓名');
                $table->string('customer_phone')->nullable()->comment('客户手机号');
                $table->text('description')->nullable()->comment('交易描述');
                $table->json('extra_data')->nullable()->comment('额外数据');
                $table->timestamp('completed_at')->nullable()->comment('完成时间');
                $table->timestamps();
                
                $table->foreign('merchant_id')->references('id')->on('merchants')->onDelete('cascade');
                $table->index(['merchant_id', 'status']);
                $table->index(['transaction_id']);
                $table->index(['created_at']);
            });
        }

        // 业务员邀请记录表
        if (!Schema::hasTable('salesman_invites')) {
            Schema::create('salesman_invites', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('inviter_id')->comment('邀请人ID');
                $table->string('phone')->comment('被邀请人手机号');
                $table->string('name')->nullable()->comment('被邀请人姓名');
                $table->string('invite_code')->unique()->comment('邀请码');
                $table->enum('status', ['pending', 'accepted', 'expired', 'cancelled'])->default('pending')->comment('邀请状态');
                $table->unsignedBigInteger('invitee_id')->nullable()->comment('被邀请人用户ID');
                $table->timestamp('accepted_at')->nullable()->comment('接受时间');
                $table->timestamp('expired_at')->nullable()->comment('过期时间');
                $table->text('remarks')->nullable()->comment('备注');
                $table->timestamps();
                
                $table->foreign('inviter_id')->references('id')->on('app_users')->onDelete('cascade');
                $table->foreign('invitee_id')->references('id')->on('app_users')->onDelete('set null');
                $table->index(['inviter_id']);
                $table->index(['phone']);
                $table->index(['invite_code']);
                $table->index(['status']);
            });
        }

        // 产品分类表
        if (!Schema::hasTable('categories')) {
            Schema::create('categories', function (Blueprint $table) {
                $table->id();
                $table->string('name')->comment('分类名称');
                $table->string('slug')->unique()->comment('分类标识');
                $table->text('description')->nullable()->comment('分类描述');
                $table->string('image')->nullable()->comment('分类图片');
                $table->unsignedBigInteger('parent_id')->nullable()->comment('父分类ID');
                $table->integer('sort')->default(0)->comment('排序');
                $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
                $table->timestamps();
                
                $table->foreign('parent_id')->references('id')->on('categories')->onDelete('set null');
                $table->index(['parent_id']);
                $table->index(['status']);
                $table->index(['sort']);
            });
        }

        // 产品表（如果不存在）
        if (!Schema::hasTable('products')) {
            Schema::create('products', function (Blueprint $table) {
                $table->id();
                $table->string('name')->comment('产品名称');
                $table->string('sku')->unique()->comment('产品SKU');
                $table->text('description')->nullable()->comment('产品描述');
                $table->text('content')->nullable()->comment('产品详情');
                $table->decimal('price', 10, 2)->comment('产品价格');
                $table->decimal('market_price', 10, 2)->nullable()->comment('市场价格');
                $table->integer('stock')->default(0)->comment('库存数量');
                $table->string('unit')->default('件')->comment('单位');
                $table->unsignedBigInteger('category_id')->nullable()->comment('分类ID');
                $table->string('brand')->nullable()->comment('品牌');
                $table->json('images')->nullable()->comment('产品图片');
                $table->json('specifications')->nullable()->comment('产品规格');
                $table->boolean('is_salesman_product')->default(false)->comment('是否为业务员可销售产品');
                $table->decimal('commission_rate', 5, 2)->default(0)->comment('佣金比例');
                $table->integer('sort')->default(0)->comment('排序');
                $table->enum('status', ['active', 'inactive', 'out_of_stock'])->default('active')->comment('状态');
                $table->timestamps();
                
                $table->foreign('category_id')->references('id')->on('categories')->onDelete('set null');
                $table->index(['category_id']);
                $table->index(['status']);
                $table->index(['is_salesman_product']);
                $table->index(['sort']);
            });
        }

        // 产品图片表
        if (!Schema::hasTable('product_images')) {
            Schema::create('product_images', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('product_id')->comment('产品ID');
                $table->string('image_url')->comment('图片URL');
                $table->string('alt_text')->nullable()->comment('图片描述');
                $table->integer('sort')->default(0)->comment('排序');
                $table->boolean('is_primary')->default(false)->comment('是否为主图');
                $table->timestamps();
                
                $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
                $table->index(['product_id']);
                $table->index(['sort']);
            });
        }

        // 产品规格表
        if (!Schema::hasTable('product_specifications')) {
            Schema::create('product_specifications', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('product_id')->comment('产品ID');
                $table->string('name')->comment('规格名称');
                $table->string('value')->comment('规格值');
                $table->integer('sort')->default(0)->comment('排序');
                $table->timestamps();
                
                $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
                $table->index(['product_id']);
                $table->index(['sort']);
            });
        }

        // 培训资料表
        if (!Schema::hasTable('training_materials')) {
            Schema::create('training_materials', function (Blueprint $table) {
                $table->id();
                $table->string('title')->comment('资料标题');
                $table->text('description')->nullable()->comment('资料描述');
                $table->enum('type', ['video', 'document', 'course', 'image'])->comment('资料类型');
                $table->string('file_url')->nullable()->comment('文件URL');
                $table->string('cover_image')->nullable()->comment('封面图片');
                $table->integer('duration')->nullable()->comment('时长（秒）');
                $table->integer('file_size')->nullable()->comment('文件大小（字节）');
                $table->string('target_audience')->comment('目标受众：salesman,merchant,all');
                $table->json('tags')->nullable()->comment('标签');
                $table->integer('view_count')->default(0)->comment('查看次数');
                $table->integer('sort')->default(0)->comment('排序');
                $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
                $table->timestamps();
                
                $table->index(['type']);
                $table->index(['target_audience']);
                $table->index(['status']);
                $table->index(['sort']);
            });
        }

        // 培训记录表
        if (!Schema::hasTable('training_records')) {
            Schema::create('training_records', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('user_id')->comment('用户ID');
                $table->unsignedBigInteger('material_id')->comment('培训资料ID');
                $table->integer('view_count')->default(1)->comment('查看次数');
                $table->integer('progress')->default(0)->comment('学习进度（百分比）');
                $table->boolean('is_completed')->default(false)->comment('是否完成');
                $table->timestamp('last_viewed_at')->nullable()->comment('最后查看时间');
                $table->timestamp('completed_at')->nullable()->comment('完成时间');
                $table->timestamps();
                
                $table->foreign('user_id')->references('id')->on('app_users')->onDelete('cascade');
                $table->foreign('material_id')->references('id')->on('training_materials')->onDelete('cascade');
                $table->unique(['user_id', 'material_id']);
                $table->index(['user_id']);
                $table->index(['material_id']);
            });
        }

        // 更新业务员佣金表结构
        if (Schema::hasTable('salesman_commissions')) {
            Schema::table('salesman_commissions', function (Blueprint $table) {
                if (!Schema::hasColumn('salesman_commissions', 'type')) {
                    $table->string('type')->default('sales')->comment('佣金类型：sales-销售佣金,referral-推荐佣金')->after('amount');
                }
                if (!Schema::hasColumn('salesman_commissions', 'customer_id')) {
                    $table->unsignedBigInteger('customer_id')->nullable()->comment('客户ID')->after('salesman_id');
                }
                if (!Schema::hasColumn('salesman_commissions', 'description')) {
                    $table->text('description')->nullable()->comment('佣金描述')->after('type');
                }
                if (!Schema::hasColumn('salesman_commissions', 'paid_at')) {
                    $table->timestamp('paid_at')->nullable()->comment('支付时间')->after('status');
                }
            });
        }

        // 更新业务员目标表结构
        if (Schema::hasTable('salesman_targets')) {
            Schema::table('salesman_targets', function (Blueprint $table) {
                if (!Schema::hasColumn('salesman_targets', 'year')) {
                    $table->integer('year')->default(2025)->comment('年份')->after('salesman_id');
                }
                if (!Schema::hasColumn('salesman_targets', 'month_vip_target')) {
                    $table->integer('month_vip_target')->default(10)->comment('月度VIP目标')->after('year');
                }
                if (!Schema::hasColumn('salesman_targets', 'month_sales_target')) {
                    $table->decimal('month_sales_target', 10, 2)->default(5000)->comment('月度销售目标')->after('month_vip_target');
                }
                if (!Schema::hasColumn('salesman_targets', 'year_vip_target')) {
                    $table->integer('year_vip_target')->default(120)->comment('年度VIP目标')->after('month_sales_target');
                }
                if (!Schema::hasColumn('salesman_targets', 'year_sales_target')) {
                    $table->decimal('year_sales_target', 10, 2)->default(60000)->comment('年度销售目标')->after('year_vip_target');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('training_records');
        Schema::dropIfExists('training_materials');
        Schema::dropIfExists('product_specifications');
        Schema::dropIfExists('product_images');
        Schema::dropIfExists('products');
        Schema::dropIfExists('categories');
        Schema::dropIfExists('salesman_invites');
        Schema::dropIfExists('merchant_transactions');
    }
};
